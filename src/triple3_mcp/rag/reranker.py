"""Reranking implementation for improving retrieval quality."""

from typing import List, <PERSON>ple

import torch
from loguru import logger
from sentence_transformers import CrossEncoder

from ..config import settings
from .chunking import TextChunk


class ChineseReranker:
    """Reranker for improving retrieval quality using cross-encoder models."""

    def __init__(
        self,
        model_name: str = settings.reranker_model_name,
        device: str = None,
        max_length: int = 512,
    ) -> None:
        """Initialize the Chinese reranker.

        Args:
            model_name: Name of the cross-encoder model
            device: Device to run the model on
            max_length: Maximum sequence length for the model
        """
        self.model_name = model_name
        self.device = device or self._get_best_device()
        self.max_length = max_length

        logger.info(f"Initializing reranker model: {model_name}")
        logger.info(f"Using device: {self.device}")

        # Load the cross-encoder model
        self.model = self._load_model()

        logger.info("Reranker model loaded successfully")

    def _load_model(self) -> CrossEncoder:
        """Load the cross-encoder model.

        Returns:
            Loaded CrossEncoder model
        """
        try:
            model = CrossEncoder(
                self.model_name, max_length=self.max_length, device=self.device
            )
            return model

        except Exception as e:
            logger.error(f"Failed to load reranker model {self.model_name}: {e}")
            raise

    def _get_best_device(self) -> str:
        """Determine the best available device for the model.

        Returns:
            Device string ('cuda', 'mps', or 'cpu')
        """
        if torch.cuda.is_available():
            return "cuda"
        elif hasattr(torch.backends, "mps") and torch.backends.mps.is_available():
            return "mps"
        else:
            return "cpu"

    def rerank(
        self,
        query: str,
        search_results: List[Tuple[TextChunk, float]],
        top_k: int = None,
    ) -> List[Tuple[TextChunk, float]]:
        """Rerank search results using cross-encoder scoring.

        Args:
            query: Original search query
            search_results: List of (TextChunk, similarity_score) tuples
            top_k: Number of top results to return after reranking

        Returns:
            Reranked list of (TextChunk, rerank_score) tuples
        """
        if not search_results:
            return []

        if top_k is None:
            top_k = settings.rerank_top_k

        # Limit input to manageable size
        candidates = search_results[
            : min(len(search_results), settings.rerank_top_k * 2)
        ]

        logger.debug(
            f"Reranking {len(candidates)} candidates for query: {query[:100]}..."
        )

        try:
            # Prepare query-document pairs for cross-encoder
            query_doc_pairs = []
            chunks = []

            for chunk, _ in candidates:
                # Truncate document text if too long
                doc_text = self._prepare_text_for_reranking(chunk.text)
                query_doc_pairs.append([query, doc_text])
                chunks.append(chunk)

            # Get cross-encoder scores
            scores = self.model.predict(query_doc_pairs)

            # Combine chunks with new scores
            reranked_results = list(zip(chunks, scores.tolist()))

            # Sort by reranking score (descending)
            reranked_results.sort(key=lambda x: x[1], reverse=True)

            # Return top_k results
            final_results = reranked_results[:top_k]

            logger.debug(
                f"Reranking complete. Returning top {len(final_results)} results"
            )
            return final_results

        except Exception as e:
            logger.error(f"Error during reranking: {e}")
            # Fallback to original ranking
            return search_results[:top_k]

    def _prepare_text_for_reranking(self, text: str) -> str:
        """Prepare text for reranking by truncating if necessary.

        Args:
            text: Original text

        Returns:
            Prepared text suitable for cross-encoder
        """
        # Remove excessive whitespace
        text = " ".join(text.split())

        # Truncate if too long (leave room for query + special tokens)
        max_doc_length = self.max_length - 100  # Reserve space for query

        if len(text) > max_doc_length:
            text = text[:max_doc_length]
            logger.debug("Document text truncated for reranking")

        return text

    def batch_rerank(
        self,
        queries: List[str],
        search_results_list: List[List[Tuple[TextChunk, float]]],
        top_k: int = None,
    ) -> List[List[Tuple[TextChunk, float]]]:
        """Rerank multiple queries in batch for efficiency.

        Args:
            queries: List of search queries
            search_results_list: List of search results for each query
            top_k: Number of top results to return for each query

        Returns:
            List of reranked results for each query
        """
        if len(queries) != len(search_results_list):
            raise ValueError("Number of queries must match number of result lists")

        reranked_results = []

        for query, search_results in zip(queries, search_results_list):
            reranked = self.rerank(query, search_results, top_k)
            reranked_results.append(reranked)

        return reranked_results

    def get_model_info(self) -> dict:
        """Get information about the loaded reranker model.

        Returns:
            Dictionary with model information
        """
        return {
            "model_name": self.model_name,
            "device": self.device,
            "max_length": self.max_length,
            "model_type": "cross-encoder",
        }
