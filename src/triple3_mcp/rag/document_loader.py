"""PDF document loader for Chinese-language Bible study materials."""

import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import fitz  # PyMuPDF
from langdetect import detect
from langdetect.lang_detect_exception import LangDetectException
from loguru import logger
from pydantic import BaseModel

from ..config import settings


class DocumentMetadata(BaseModel):
    """Metadata for a loaded document."""

    filename: str
    file_path: str
    page_count: int
    language: Optional[str] = None
    title: Optional[str] = None
    author: Optional[str] = None
    creation_date: Optional[str] = None
    modification_date: Optional[str] = None
    file_size: int


class DocumentPage(BaseModel):
    """Represents a single page from a document."""

    page_number: int
    text_content: str
    metadata: DocumentMetadata


class PDFDocumentLoader:
    """Loads and processes PDF documents with Chinese text support."""

    def __init__(self) -> None:
        """Initialize the PDF document loader."""
        self.supported_extensions = {".pdf"}

    def load_document(self, file_path: Path) -> List[DocumentPage]:
        """Load a single PDF document and extract text content.

        Args:
            file_path: Path to the PDF file

        Returns:
            List of DocumentPage objects containing extracted text

        Raises:
            ValueError: If file format is not supported
            FileNotFoundError: If file does not exist
        """
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        if file_path.suffix.lower() not in self.supported_extensions:
            raise ValueError(f"Unsupported file format: {file_path.suffix}")

        logger.info(f"Loading PDF document: {file_path}")

        try:
            doc = fitz.open(file_path)
            metadata = self._extract_metadata(doc, file_path)
            pages = []

            for page_num in range(len(doc)):
                page = doc[page_num]
                text_content = self._extract_text_from_page(page)

                if text_content.strip():  # Only include pages with content
                    doc_page = DocumentPage(
                        page_number=page_num + 1,
                        text_content=text_content,
                        metadata=metadata,
                    )
                    pages.append(doc_page)

            doc.close()
            logger.info(f"Successfully loaded {len(pages)} pages from {file_path}")
            return pages

        except Exception as e:
            logger.error(f"Error loading PDF {file_path}: {e}")
            raise

    def load_documents_from_directory(self, directory: Path) -> List[DocumentPage]:
        """Load all PDF documents from a directory.

        Args:
            directory: Directory containing PDF files

        Returns:
            List of all DocumentPage objects from all PDFs
        """
        if not directory.exists():
            logger.warning(f"Directory does not exist: {directory}")
            return []

        all_pages = []
        pdf_files = list(directory.glob("**/*.pdf"))

        if not pdf_files:
            logger.warning(f"No PDF files found in directory: {directory}")
            return []

        logger.info(f"Found {len(pdf_files)} PDF files in {directory}")

        for pdf_file in pdf_files:
            try:
                pages = self.load_document(pdf_file)
                all_pages.extend(pages)
            except Exception as e:
                logger.error(f"Failed to load {pdf_file}: {e}")
                continue

        logger.info(f"Successfully loaded {len(all_pages)} total pages")
        return all_pages

    def _extract_metadata(
        self, doc: fitz.Document, file_path: Path
    ) -> DocumentMetadata:
        """Extract metadata from PDF document.

        Args:
            doc: PyMuPDF document object
            file_path: Path to the PDF file

        Returns:
            DocumentMetadata object
        """
        metadata_dict = doc.metadata
        file_stats = file_path.stat()

        # Detect language from first few pages
        language = self._detect_document_language(doc)

        return DocumentMetadata(
            filename=file_path.name,
            file_path=str(file_path),
            page_count=len(doc),
            language=language,
            title=metadata_dict.get("title"),
            author=metadata_dict.get("author"),
            creation_date=metadata_dict.get("creationDate"),
            modification_date=metadata_dict.get("modDate"),
            file_size=file_stats.st_size,
        )

    def _extract_text_from_page(self, page: fitz.Page) -> str:
        """Extract and clean text from a PDF page.

        Args:
            page: PyMuPDF page object

        Returns:
            Cleaned text content
        """
        # Extract text with layout preservation
        text = page.get_text("text")

        # Clean and normalize the text
        text = self._clean_text(text)

        return text

    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text.

        Args:
            text: Raw extracted text

        Returns:
            Cleaned text
        """
        # Remove excessive whitespace
        text = re.sub(r"\s+", " ", text)

        # Remove page numbers and headers/footers (common patterns)
        text = re.sub(r"^\d+\s*$", "", text, flags=re.MULTILINE)

        # Remove excessive line breaks
        text = re.sub(r"\n\s*\n\s*\n+", "\n\n", text)

        # Strip leading/trailing whitespace
        text = text.strip()

        return text

    def _detect_document_language(self, doc: fitz.Document) -> Optional[str]:
        """Detect the primary language of the document.

        Args:
            doc: PyMuPDF document object

        Returns:
            Detected language code or None
        """
        # Sample text from first few pages
        sample_text = ""
        max_pages = min(3, len(doc))

        for page_num in range(max_pages):
            page_text = doc[page_num].get_text("text")
            sample_text += page_text[:1000]  # First 1000 chars per page

        if len(sample_text.strip()) < 50:
            return None

        try:
            detected_lang = detect(sample_text)
            if detected_lang in settings.supported_languages:
                return detected_lang
        except LangDetectException:
            logger.warning("Could not detect document language")

        return None
