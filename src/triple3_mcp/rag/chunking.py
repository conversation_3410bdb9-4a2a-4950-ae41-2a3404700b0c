"""Text chunking strategies optimized for Chinese text."""

import re
from typing import List, Optional

import jieba
from loguru import logger
from pydantic import BaseModel

from ..config import settings
from .document_loader import DocumentPage


class TextChunk(BaseModel):
    """Represents a chunk of text with metadata."""

    chunk_id: str
    text: str
    source_document: str
    page_number: int
    chunk_index: int
    start_char: int
    end_char: int
    word_count: int
    language: Optional[str] = None


class ChineseTextChunker:
    """Text chunker optimized for Chinese-language documents."""

    def __init__(
        self,
        chunk_size: int = settings.chunk_size,
        chunk_overlap: int = settings.chunk_overlap,
        min_chunk_size: int = settings.min_chunk_size,
    ) -> None:
        """Initialize the Chinese text chunker.

        Args:
            chunk_size: Maximum size of chunks in characters
            chunk_overlap: Overlap between consecutive chunks in characters
            min_chunk_size: Minimum size of chunks in characters
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.min_chunk_size = min_chunk_size

        # Initialize jieba for Chinese word segmentation
        jieba.initialize()

        # Chinese sentence ending patterns
        self.chinese_sentence_endings = re.compile(r"[。！？；]")
        self.paragraph_separators = re.compile(r"\n\s*\n")

    def chunk_documents(self, documents: List[DocumentPage]) -> List[TextChunk]:
        """Chunk a list of documents into smaller text segments.

        Args:
            documents: List of DocumentPage objects to chunk

        Returns:
            List of TextChunk objects
        """
        all_chunks = []

        for doc in documents:
            try:
                doc_chunks = self.chunk_document(doc)
                all_chunks.extend(doc_chunks)
            except Exception as e:
                logger.error(f"Error chunking document {doc.metadata.filename}: {e}")
                continue

        logger.info(f"Created {len(all_chunks)} chunks from {len(documents)} documents")
        return all_chunks

    def chunk_document(self, document: DocumentPage) -> List[TextChunk]:
        """Chunk a single document page into smaller text segments.

        Args:
            document: DocumentPage to chunk

        Returns:
            List of TextChunk objects
        """
        text = document.text_content
        if len(text) < self.min_chunk_size:
            # Document too small, return as single chunk
            chunk = TextChunk(
                chunk_id=self._generate_chunk_id(document, 0),
                text=text,
                source_document=document.metadata.filename,
                page_number=document.page_number,
                chunk_index=0,
                start_char=0,
                end_char=len(text),
                word_count=self._count_words(text),
                language=document.metadata.language,
            )
            return [chunk]

        # Use semantic chunking for better results
        chunks = self._semantic_chunk(text, document)
        return chunks

    def _semantic_chunk(self, text: str, document: DocumentPage) -> List[TextChunk]:
        """Perform semantic chunking that respects sentence and paragraph boundaries.

        Args:
            text: Text to chunk
            document: Source document metadata

        Returns:
            List of TextChunk objects
        """
        chunks = []

        # First, split by paragraphs
        paragraphs = self.paragraph_separators.split(text)

        current_chunk = ""
        current_start = 0
        chunk_index = 0

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # If adding this paragraph would exceed chunk size, finalize current chunk
            if (
                len(current_chunk) + len(paragraph) > self.chunk_size
                and len(current_chunk) >= self.min_chunk_size
            ):

                chunk = self._create_chunk(
                    current_chunk, document, chunk_index, current_start
                )
                chunks.append(chunk)

                # Start new chunk with overlap
                overlap_text = self._get_overlap_text(current_chunk)
                current_chunk = overlap_text + paragraph
                current_start = self._find_start_position(
                    text, current_chunk, current_start
                )
                chunk_index += 1
            else:
                # Add paragraph to current chunk
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
                    current_start = text.find(paragraph, current_start)

        # Add final chunk if it has content
        if current_chunk.strip() and len(current_chunk) >= self.min_chunk_size:
            chunk = self._create_chunk(
                current_chunk, document, chunk_index, current_start
            )
            chunks.append(chunk)

        # If no chunks were created (text too short), create one chunk
        if not chunks and text.strip():
            chunk = self._create_chunk(text, document, 0, 0)
            chunks.append(chunk)

        return chunks

    def _create_chunk(
        self, text: str, document: DocumentPage, chunk_index: int, start_char: int
    ) -> TextChunk:
        """Create a TextChunk object.

        Args:
            text: Chunk text content
            document: Source document
            chunk_index: Index of this chunk within the document
            start_char: Starting character position in original text

        Returns:
            TextChunk object
        """
        return TextChunk(
            chunk_id=self._generate_chunk_id(document, chunk_index),
            text=text.strip(),
            source_document=document.metadata.filename,
            page_number=document.page_number,
            chunk_index=chunk_index,
            start_char=start_char,
            end_char=start_char + len(text),
            word_count=self._count_words(text),
            language=document.metadata.language,
        )

    def _get_overlap_text(self, text: str) -> str:
        """Get overlap text from the end of current chunk.

        Args:
            text: Current chunk text

        Returns:
            Overlap text for next chunk
        """
        if len(text) <= self.chunk_overlap:
            return text

        # Try to find a good sentence boundary for overlap
        overlap_start = len(text) - self.chunk_overlap
        sentences = self.chinese_sentence_endings.split(text[overlap_start:])

        if len(sentences) > 1:
            # Use complete sentences for overlap
            return (
                sentences[-2] + sentences[-1] if len(sentences) >= 2 else sentences[-1]
            )
        else:
            # Fallback to character-based overlap
            return text[-self.chunk_overlap :]

    def _find_start_position(
        self, full_text: str, chunk_text: str, hint_pos: int
    ) -> int:
        """Find the starting position of chunk text in full text.

        Args:
            full_text: Complete document text
            chunk_text: Chunk text to find
            hint_pos: Hint position to start searching from

        Returns:
            Starting character position
        """
        # Remove overlap prefix and search for the main content
        search_text = chunk_text[:100]  # Use first 100 chars for search
        pos = full_text.find(search_text, hint_pos)
        return pos if pos != -1 else hint_pos

    def _count_words(self, text: str) -> int:
        """Count words in text, handling both Chinese and English.

        Args:
            text: Text to count words in

        Returns:
            Word count
        """
        # Use jieba for Chinese word segmentation
        words = jieba.lcut(text)
        # Filter out whitespace and punctuation
        meaningful_words = [
            w for w in words if w.strip() and not re.match(r"^[^\w]+$", w)
        ]
        return len(meaningful_words)

    def _generate_chunk_id(self, document: DocumentPage, chunk_index: int) -> str:
        """Generate a unique ID for a chunk.

        Args:
            document: Source document
            chunk_index: Index of chunk within document

        Returns:
            Unique chunk ID
        """
        filename = document.metadata.filename.replace(".pdf", "")
        return f"{filename}_page{document.page_number}_chunk{chunk_index}"
