"""Embedding management for Chinese text using sentence transformers."""

from pathlib import Path
from typing import List, Optional

import numpy as np
import torch
from loguru import logger
from sentence_transformers import SentenceTransformer

from ..config import settings
from .chunking import TextChunk


class ChineseEmbeddingManager:
    """Manages embeddings for Chinese text using sentence transformers."""

    def __init__(
        self,
        model_name: str = settings.embedding_model_name,
        cache_dir: Optional[Path] = None,
        device: Optional[str] = None,
    ) -> None:
        """Initialize the Chinese embedding manager.

        Args:
            model_name: Name of the sentence transformer model
            cache_dir: Directory to cache the model
            device: Device to run the model on ('cpu', 'cuda', 'mps')
        """
        self.model_name = model_name
        self.cache_dir = cache_dir or settings.cache_directory
        self.device = device or self._get_best_device()

        logger.info(f"Initializing embedding model: {model_name}")
        logger.info(f"Using device: {self.device}")

        # Initialize the model
        self.model = self._load_model()
        self.embedding_dimension = self.model.get_sentence_embedding_dimension()

        logger.info(
            f"Model loaded successfully. Embedding dimension: {self.embedding_dimension}"
        )

    def _load_model(self) -> SentenceTransformer:
        """Load the sentence transformer model.

        Returns:
            Loaded SentenceTransformer model
        """
        try:
            model = SentenceTransformer(
                self.model_name, cache_folder=str(self.cache_dir), device=self.device
            )
            return model
        except Exception as e:
            logger.error(f"Failed to load embedding model {self.model_name}: {e}")
            raise

    def _get_best_device(self) -> str:
        """Determine the best available device for the model.

        Returns:
            Device string ('cuda', 'mps', or 'cpu')
        """
        if torch.cuda.is_available():
            return "cuda"
        elif hasattr(torch.backends, "mps") and torch.backends.mps.is_available():
            return "mps"
        else:
            return "cpu"

    def embed_texts(self, texts: List[str]) -> np.ndarray:
        """Generate embeddings for a list of texts.

        Args:
            texts: List of text strings to embed

        Returns:
            NumPy array of embeddings with shape (len(texts), embedding_dimension)
        """
        if not texts:
            return np.array([]).reshape(0, self.embedding_dimension)

        logger.debug(f"Generating embeddings for {len(texts)} texts")

        try:
            # Generate embeddings in batches for memory efficiency
            embeddings = self.model.encode(
                texts,
                batch_size=settings.batch_size,
                show_progress_bar=len(texts) > 100,
                convert_to_numpy=True,
                normalize_embeddings=True,  # Normalize for cosine similarity
            )

            logger.debug(f"Generated embeddings with shape: {embeddings.shape}")
            return embeddings

        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            raise

    def embed_chunks(self, chunks: List[TextChunk]) -> List[np.ndarray]:
        """Generate embeddings for text chunks.

        Args:
            chunks: List of TextChunk objects

        Returns:
            List of embedding arrays
        """
        if not chunks:
            return []

        texts = [chunk.text for chunk in chunks]
        embeddings = self.embed_texts(texts)

        # Convert to list of individual arrays
        return [embeddings[i] for i in range(len(embeddings))]

    def embed_query(self, query: str) -> np.ndarray:
        """Generate embedding for a single query.

        Args:
            query: Query string to embed

        Returns:
            NumPy array embedding
        """
        logger.debug(f"Generating embedding for query: {query[:100]}...")

        try:
            embedding = self.model.encode(
                [query], convert_to_numpy=True, normalize_embeddings=True
            )[0]

            return embedding

        except Exception as e:
            logger.error(f"Error generating query embedding: {e}")
            raise

    def compute_similarity(
        self, query_embedding: np.ndarray, document_embeddings: List[np.ndarray]
    ) -> List[float]:
        """Compute cosine similarity between query and document embeddings.

        Args:
            query_embedding: Query embedding vector
            document_embeddings: List of document embedding vectors

        Returns:
            List of similarity scores
        """
        if not document_embeddings:
            return []

        # Stack document embeddings
        doc_matrix = np.vstack(document_embeddings)

        # Compute cosine similarity
        similarities = np.dot(doc_matrix, query_embedding)

        return similarities.tolist()

    def get_model_info(self) -> dict:
        """Get information about the loaded model.

        Returns:
            Dictionary with model information
        """
        return {
            "model_name": self.model_name,
            "embedding_dimension": self.embedding_dimension,
            "device": self.device,
            "max_sequence_length": getattr(self.model, "max_seq_length", "unknown"),
            "cache_dir": str(self.cache_dir),
        }

    def preprocess_text_for_embedding(self, text: str) -> str:
        """Preprocess text before embedding generation.

        Args:
            text: Raw text to preprocess

        Returns:
            Preprocessed text
        """
        # Basic preprocessing for Chinese text
        text = text.strip()

        # Remove excessive whitespace
        import re

        text = re.sub(r"\s+", " ", text)

        # Truncate if too long (model-dependent)
        max_length = getattr(self.model, "max_seq_length", 512)
        if len(text) > max_length:
            text = text[:max_length]
            logger.warning(f"Text truncated to {max_length} characters")

        return text
