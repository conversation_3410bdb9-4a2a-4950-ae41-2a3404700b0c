"""Vector store implementation using ChromaDB for document storage and retrieval."""

import json
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import chromadb
import numpy as np
from chromadb.config import Settings as ChromaSettings
from loguru import logger

from ..config import settings
from .chunking import TextChunk
from .embeddings import ChineseEmbeddingManager


class ChromaVectorStore:
    """Vector store using ChromaDB for storing and retrieving document embeddings."""

    def __init__(
        self,
        persist_directory: Optional[Path] = None,
        collection_name: str = settings.collection_name,
        embedding_manager: Optional[ChineseEmbeddingManager] = None,
    ) -> None:
        """Initialize the ChromaDB vector store.

        Args:
            persist_directory: Directory to persist the database
            collection_name: Name of the collection to use
            embedding_manager: Embedding manager instance
        """
        self.persist_directory = persist_directory or settings.vector_db_path
        self.collection_name = collection_name
        self.embedding_manager = embedding_manager or ChineseEmbeddingManager()

        # Ensure persist directory exists
        self.persist_directory.mkdir(parents=True, exist_ok=True)

        # Initialize ChromaDB client
        self.client = self._initialize_client()
        self.collection = self._get_or_create_collection()

        logger.info(f"ChromaDB vector store initialized at {self.persist_directory}")

    def _initialize_client(self) -> chromadb.Client:
        """Initialize the ChromaDB client.

        Returns:
            ChromaDB client instance
        """
        try:
            client = chromadb.PersistentClient(
                path=str(self.persist_directory),
                settings=ChromaSettings(anonymized_telemetry=False, allow_reset=True),
            )
            logger.info("ChromaDB client initialized successfully")
            return client

        except Exception as e:
            logger.error(f"Failed to initialize ChromaDB client: {e}")
            raise

    def _get_or_create_collection(self) -> chromadb.Collection:
        """Get existing collection or create a new one.

        Returns:
            ChromaDB collection instance
        """
        try:
            # Try to get existing collection
            collection = self.client.get_collection(name=self.collection_name)
            logger.info(f"Using existing collection: {self.collection_name}")

        except ValueError:
            # Collection doesn't exist, create it
            collection = self.client.create_collection(
                name=self.collection_name,
                metadata={
                    "description": "Chinese Bible study materials",
                    "embedding_model": self.embedding_manager.model_name,
                    "embedding_dimension": self.embedding_manager.embedding_dimension,
                },
            )
            logger.info(f"Created new collection: {self.collection_name}")

        return collection

    def add_chunks(self, chunks: List[TextChunk]) -> None:
        """Add text chunks to the vector store.

        Args:
            chunks: List of TextChunk objects to add
        """
        if not chunks:
            logger.warning("No chunks provided to add")
            return

        logger.info(f"Adding {len(chunks)} chunks to vector store")

        # Generate embeddings for all chunks
        texts = [chunk.text for chunk in chunks]
        embeddings = self.embedding_manager.embed_texts(texts)

        # Prepare data for ChromaDB
        ids = [chunk.chunk_id for chunk in chunks]
        documents = texts
        metadatas = [self._chunk_to_metadata(chunk) for chunk in chunks]
        embeddings_list = embeddings.tolist()

        try:
            # Add to collection in batches
            batch_size = 100
            for i in range(0, len(chunks), batch_size):
                end_idx = min(i + batch_size, len(chunks))

                self.collection.add(
                    ids=ids[i:end_idx],
                    documents=documents[i:end_idx],
                    metadatas=metadatas[i:end_idx],
                    embeddings=embeddings_list[i:end_idx],
                )

            logger.info(f"Successfully added {len(chunks)} chunks to vector store")

        except Exception as e:
            logger.error(f"Error adding chunks to vector store: {e}")
            raise

    def search(
        self,
        query: str,
        limit: int = settings.default_search_limit,
        where: Optional[Dict[str, Any]] = None,
        similarity_threshold: float = settings.similarity_threshold,
    ) -> List[Tuple[TextChunk, float]]:
        """Search for similar documents using semantic similarity.

        Args:
            query: Search query string
            limit: Maximum number of results to return
            where: Optional metadata filters
            similarity_threshold: Minimum similarity score

        Returns:
            List of tuples containing (TextChunk, similarity_score)
        """
        if not query.strip():
            logger.warning("Empty query provided")
            return []

        logger.debug(f"Searching for: {query[:100]}...")

        try:
            # Generate query embedding
            query_embedding = self.embedding_manager.embed_query(query)

            # Search in ChromaDB
            results = self.collection.query(
                query_embeddings=[query_embedding.tolist()],
                n_results=min(limit, settings.max_search_limit),
                where=where,
                include=["documents", "metadatas", "distances"],
            )

            # Process results
            search_results = []

            if results["ids"] and results["ids"][0]:
                for i, chunk_id in enumerate(results["ids"][0]):
                    distance = results["distances"][0][i]
                    similarity = 1 - distance  # Convert distance to similarity

                    if similarity >= similarity_threshold:
                        metadata = results["metadatas"][0][i]
                        document = results["documents"][0][i]

                        # Reconstruct TextChunk
                        chunk = self._metadata_to_chunk(chunk_id, document, metadata)
                        search_results.append((chunk, similarity))

            logger.debug(
                f"Found {len(search_results)} results above threshold {similarity_threshold}"
            )
            return search_results

        except Exception as e:
            logger.error(f"Error during search: {e}")
            raise

    def get_chunk_by_id(self, chunk_id: str) -> Optional[TextChunk]:
        """Retrieve a specific chunk by its ID.

        Args:
            chunk_id: Unique chunk identifier

        Returns:
            TextChunk object or None if not found
        """
        try:
            results = self.collection.get(
                ids=[chunk_id], include=["documents", "metadatas"]
            )

            if results["ids"] and results["ids"][0]:
                metadata = results["metadatas"][0]
                document = results["documents"][0]
                return self._metadata_to_chunk(chunk_id, document, metadata)

        except Exception as e:
            logger.error(f"Error retrieving chunk {chunk_id}: {e}")

        return None

    def delete_chunks(self, chunk_ids: List[str]) -> None:
        """Delete chunks from the vector store.

        Args:
            chunk_ids: List of chunk IDs to delete
        """
        if not chunk_ids:
            return

        try:
            self.collection.delete(ids=chunk_ids)
            logger.info(f"Deleted {len(chunk_ids)} chunks from vector store")

        except Exception as e:
            logger.error(f"Error deleting chunks: {e}")
            raise

    def clear_collection(self) -> None:
        """Clear all documents from the collection."""
        try:
            self.client.delete_collection(name=self.collection_name)
            self.collection = self._get_or_create_collection()
            logger.info("Collection cleared successfully")

        except Exception as e:
            logger.error(f"Error clearing collection: {e}")
            raise

    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the collection.

        Returns:
            Dictionary with collection statistics
        """
        try:
            count = self.collection.count()

            return {
                "collection_name": self.collection_name,
                "document_count": count,
                "embedding_model": self.embedding_manager.model_name,
                "embedding_dimension": self.embedding_manager.embedding_dimension,
                "persist_directory": str(self.persist_directory),
            }

        except Exception as e:
            logger.error(f"Error getting collection stats: {e}")
            return {}

    def _chunk_to_metadata(self, chunk: TextChunk) -> Dict[str, Any]:
        """Convert TextChunk to ChromaDB metadata format.

        Args:
            chunk: TextChunk object

        Returns:
            Metadata dictionary
        """
        return {
            "source_document": chunk.source_document,
            "page_number": chunk.page_number,
            "chunk_index": chunk.chunk_index,
            "start_char": chunk.start_char,
            "end_char": chunk.end_char,
            "word_count": chunk.word_count,
            "language": chunk.language or "unknown",
        }

    def _metadata_to_chunk(
        self, chunk_id: str, document: str, metadata: Dict[str, Any]
    ) -> TextChunk:
        """Convert ChromaDB metadata back to TextChunk.

        Args:
            chunk_id: Chunk identifier
            document: Document text
            metadata: Metadata dictionary

        Returns:
            TextChunk object
        """
        return TextChunk(
            chunk_id=chunk_id,
            text=document,
            source_document=metadata["source_document"],
            page_number=metadata["page_number"],
            chunk_index=metadata["chunk_index"],
            start_char=metadata["start_char"],
            end_char=metadata["end_char"],
            word_count=metadata["word_count"],
            language=metadata.get("language"),
        )
