"""Triple3 MCP Server for Chinese Bible Study Materials.

This package provides a Model Context Protocol (MCP) server that enables
Retrieval-Augmented Generation (RAG) capabilities for Chinese-language
Bible study materials.

The server loads PDF documents, builds a vector database using embeddings,
and exposes MCP tools for semantic search and question answering.
"""

__version__ = "0.1.0"
__author__ = "Peilun Dai"
__email__ = "<EMAIL>"

from .config import Settings
from .server import MCPServer

__all__ = ["Settings", "MCPServer"]
