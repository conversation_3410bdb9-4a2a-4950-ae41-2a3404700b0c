"""Configuration management for Triple3 MCP Server."""

import os
from pathlib import Path
from typing import Any, List, Optional

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support."""

    # Server Configuration
    server_name: str = Field(default="triple3-mcp", description="MCP server name")
    server_version: str = Field(default="0.1.0", description="Server version")
    log_level: str = Field(default="INFO", description="Logging level")

    # File and Directory Paths
    files_directory: Path = Field(
        default=Path("files"), description="Directory containing PDF documents"
    )
    vector_db_path: Path = Field(
        default=Path("data/vector_db"), description="Path to vector database storage"
    )
    cache_directory: Path = Field(
        default=Path("data/cache"),
        description="Directory for caching embeddings and models",
    )

    # Embedding Configuration
    embedding_model_name: str = Field(
        default="sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
        description="Sentence transformer model for embeddings",
    )
    embedding_dimension: int = Field(
        default=384, description="Dimension of embedding vectors"
    )

    # Reranking Configuration
    reranker_model_name: str = Field(
        default="cross-encoder/ms-marco-MiniLM-L-12-v2",
        description="Cross-encoder model for reranking",
    )
    rerank_top_k: int = Field(default=10, description="Number of documents to rerank")

    # Chunking Configuration
    chunk_size: int = Field(
        default=512, description="Maximum size of text chunks in characters"
    )
    chunk_overlap: int = Field(
        default=50, description="Overlap between consecutive chunks in characters"
    )
    min_chunk_size: int = Field(
        default=100, description="Minimum size of text chunks in characters"
    )

    # Search Configuration
    default_search_limit: int = Field(
        default=5, description="Default number of search results to return"
    )
    max_search_limit: int = Field(
        default=20, description="Maximum number of search results allowed"
    )
    similarity_threshold: float = Field(
        default=0.7, description="Minimum similarity score for search results"
    )

    # Vector Database Configuration
    collection_name: str = Field(
        default="chinese_bible_study", description="ChromaDB collection name"
    )
    distance_metric: str = Field(
        default="cosine", description="Distance metric for vector similarity"
    )

    # PDF Processing Configuration
    supported_languages: List[str] = Field(
        default=["zh", "zh-cn", "zh-tw", "en"],
        description="Supported languages for document processing",
    )
    extract_images: bool = Field(
        default=False, description="Whether to extract and process images from PDFs"
    )

    # Performance Configuration
    batch_size: int = Field(
        default=32, description="Batch size for embedding generation"
    )
    max_workers: int = Field(default=4, description="Maximum number of worker threads")

    # Cloud Run Configuration
    port: int = Field(
        default=int(os.getenv("PORT", "8080")),
        description="Server port (Cloud Run uses PORT env var)",
    )
    host: str = Field(default="0.0.0.0", description="Server host")

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
    }

    def model_post_init(self, __context: Any) -> None:
        """Create necessary directories after initialization."""
        self.files_directory.mkdir(parents=True, exist_ok=True)
        self.vector_db_path.parent.mkdir(parents=True, exist_ok=True)
        self.cache_directory.mkdir(parents=True, exist_ok=True)


# Global settings instance
settings = Settings()
