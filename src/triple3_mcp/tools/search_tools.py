"""MCP tools for searching Chinese Bible study materials."""

import json
from typing import Any, Dict, List, Optional

from loguru import logger
from mcp.types import Text<PERSON>ontent, Tool
from pydantic import BaseModel, Field

from ..config import settings
from ..rag import ChineseEmbeddingManager, ChineseReranker, ChromaVectorStore


class SearchRequest(BaseModel):
    """Request model for search operations."""

    query: str = Field(..., description="Search query in Chinese or English")
    limit: int = Field(
        default=settings.default_search_limit,
        ge=1,
        le=settings.max_search_limit,
        description="Maximum number of results to return",
    )
    use_reranking: bool = Field(
        default=True, description="Whether to use reranking for better results"
    )
    similarity_threshold: float = Field(
        default=settings.similarity_threshold,
        ge=0.0,
        le=1.0,
        description="Minimum similarity score for results",
    )
    source_filter: Optional[str] = Field(
        default=None, description="Filter results by source document name"
    )


class SearchResult(BaseModel):
    """Result model for search operations."""

    chunk_id: str
    text: str
    source_document: str
    page_number: int
    similarity_score: float
    word_count: int
    language: Optional[str] = None


class SearchResponse(BaseModel):
    """Response model for search operations."""

    query: str
    results: List[SearchResult]
    total_found: int
    search_time_ms: float
    used_reranking: bool


class SearchTools:
    """MCP tools for searching Chinese Bible study materials."""

    def __init__(
        self,
        vector_store: ChromaVectorStore,
        embedding_manager: ChineseEmbeddingManager,
        reranker: Optional[ChineseReranker] = None,
    ) -> None:
        """Initialize search tools.

        Args:
            vector_store: Vector store instance
            embedding_manager: Embedding manager instance
            reranker: Optional reranker instance
        """
        self.vector_store = vector_store
        self.embedding_manager = embedding_manager
        self.reranker = reranker

    def get_tools(self) -> List[Tool]:
        """Get list of available MCP tools.

        Returns:
            List of Tool objects
        """
        return [
            Tool(
                name="search_bible_materials",
                description="Search Chinese Bible study materials using semantic similarity",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "Search query in Chinese or English",
                        },
                        "limit": {
                            "type": "integer",
                            "minimum": 1,
                            "maximum": settings.max_search_limit,
                            "default": settings.default_search_limit,
                            "description": "Maximum number of results to return",
                        },
                        "use_reranking": {
                            "type": "boolean",
                            "default": True,
                            "description": "Whether to use reranking for better results",
                        },
                        "similarity_threshold": {
                            "type": "number",
                            "minimum": 0.0,
                            "maximum": 1.0,
                            "default": settings.similarity_threshold,
                            "description": "Minimum similarity score for results",
                        },
                        "source_filter": {
                            "type": "string",
                            "description": "Filter results by source document name (optional)",
                        },
                    },
                    "required": ["query"],
                },
            ),
            Tool(
                name="get_document_info",
                description="Get information about available documents in the knowledge base",
                inputSchema={"type": "object", "properties": {}, "required": []},
            ),
            Tool(
                name="get_chunk_by_id",
                description="Retrieve a specific text chunk by its ID",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "chunk_id": {
                            "type": "string",
                            "description": "Unique identifier of the text chunk",
                        }
                    },
                    "required": ["chunk_id"],
                },
            ),
        ]

    async def handle_search_bible_materials(
        self, arguments: Dict[str, Any]
    ) -> List[TextContent]:
        """Handle search_bible_materials tool call.

        Args:
            arguments: Tool arguments

        Returns:
            List of TextContent responses
        """
        try:
            # Validate and parse request
            request = SearchRequest(**arguments)

            logger.info(f"Searching for: {request.query}")

            import time

            start_time = time.time()

            # Build metadata filter if source filter is provided
            where_filter = None
            if request.source_filter:
                where_filter = {"source_document": {"$eq": request.source_filter}}

            # Perform initial search
            search_results = self.vector_store.search(
                query=request.query,
                limit=request.limit * 2 if request.use_reranking else request.limit,
                where=where_filter,
                similarity_threshold=request.similarity_threshold,
            )

            # Apply reranking if requested and available
            if request.use_reranking and self.reranker and search_results:
                search_results = self.reranker.rerank(
                    query=request.query,
                    search_results=search_results,
                    top_k=request.limit,
                )

            search_time = (time.time() - start_time) * 1000

            # Convert to response format
            results = []
            for chunk, score in search_results:
                result = SearchResult(
                    chunk_id=chunk.chunk_id,
                    text=chunk.text,
                    source_document=chunk.source_document,
                    page_number=chunk.page_number,
                    similarity_score=score,
                    word_count=chunk.word_count,
                    language=chunk.language,
                )
                results.append(result)

            response = SearchResponse(
                query=request.query,
                results=results,
                total_found=len(results),
                search_time_ms=search_time,
                used_reranking=request.use_reranking and self.reranker is not None,
            )

            return [
                TextContent(
                    type="text",
                    text=json.dumps(response.dict(), ensure_ascii=False, indent=2),
                )
            ]

        except Exception as e:
            logger.error(f"Error in search_bible_materials: {e}")
            error_response = {
                "error": str(e),
                "query": arguments.get("query", ""),
                "results": [],
                "total_found": 0,
            }
            return [
                TextContent(
                    type="text",
                    text=json.dumps(error_response, ensure_ascii=False, indent=2),
                )
            ]

    async def handle_get_document_info(
        self, arguments: Dict[str, Any]
    ) -> List[TextContent]:
        """Handle get_document_info tool call.

        Args:
            arguments: Tool arguments (unused)

        Returns:
            List of TextContent responses
        """
        try:
            stats = self.vector_store.get_collection_stats()

            # Get unique source documents
            # Note: This is a simplified approach. In production, you might want to
            # store document metadata separately for more efficient queries.
            all_results = self.vector_store.collection.get(include=["metadatas"])

            source_docs = set()
            if all_results["metadatas"]:
                for metadata in all_results["metadatas"]:
                    source_docs.add(metadata["source_document"])

            document_info = {
                "collection_stats": stats,
                "available_documents": sorted(list(source_docs)),
                "total_documents": len(source_docs),
                "embedding_model": self.embedding_manager.get_model_info(),
                "reranker_available": self.reranker is not None,
            }

            if self.reranker:
                document_info["reranker_model"] = self.reranker.get_model_info()

            return [
                TextContent(
                    type="text",
                    text=json.dumps(document_info, ensure_ascii=False, indent=2),
                )
            ]

        except Exception as e:
            logger.error(f"Error in get_document_info: {e}")
            error_response = {
                "error": str(e),
                "collection_stats": {},
                "available_documents": [],
                "total_documents": 0,
            }
            return [
                TextContent(
                    type="text",
                    text=json.dumps(error_response, ensure_ascii=False, indent=2),
                )
            ]

    async def handle_get_chunk_by_id(
        self, arguments: Dict[str, Any]
    ) -> List[TextContent]:
        """Handle get_chunk_by_id tool call.

        Args:
            arguments: Tool arguments

        Returns:
            List of TextContent responses
        """
        try:
            chunk_id = arguments.get("chunk_id")
            if not chunk_id:
                raise ValueError("chunk_id is required")

            chunk = self.vector_store.get_chunk_by_id(chunk_id)

            if chunk:
                chunk_data = {
                    "chunk_id": chunk.chunk_id,
                    "text": chunk.text,
                    "source_document": chunk.source_document,
                    "page_number": chunk.page_number,
                    "chunk_index": chunk.chunk_index,
                    "start_char": chunk.start_char,
                    "end_char": chunk.end_char,
                    "word_count": chunk.word_count,
                    "language": chunk.language,
                    "found": True,
                }
            else:
                chunk_data = {
                    "chunk_id": chunk_id,
                    "found": False,
                    "error": "Chunk not found",
                }

            return [
                TextContent(
                    type="text",
                    text=json.dumps(chunk_data, ensure_ascii=False, indent=2),
                )
            ]

        except Exception as e:
            logger.error(f"Error in get_chunk_by_id: {e}")
            error_response = {
                "chunk_id": arguments.get("chunk_id", ""),
                "found": False,
                "error": str(e),
            }
            return [
                TextContent(
                    type="text",
                    text=json.dumps(error_response, ensure_ascii=False, indent=2),
                )
            ]
