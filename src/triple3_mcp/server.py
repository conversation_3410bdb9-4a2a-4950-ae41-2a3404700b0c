"""Main MCP server for Chinese Bible study materials."""

import asyncio
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional

from loguru import logger
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import TextContent

from .config import settings
from .rag import (
    ChineseEmbeddingManager,
    ChineseReranker,
    ChineseTextChunker,
    ChromaVectorStore,
    PDFDocumentLoader,
)
from .tools import SearchTools


class MCPServer:
    """MCP server for Chinese Bible study materials with RAG capabilities."""

    def __init__(self) -> None:
        """Initialize the MCP server."""
        self.server = Server(settings.server_name)
        self.search_tools: Optional[SearchTools] = None
        self.vector_store: Optional[ChromaVectorStore] = None
        self.embedding_manager: Optional[ChineseEmbeddingManager] = None
        self.reranker: Optional[ChineseReranker] = None
        self.is_initialized = False

        # Configure logging
        self._configure_logging()

        # Register handlers
        self._register_handlers()

    def _configure_logging(self) -> None:
        """Configure logging for the server."""
        logger.remove()  # Remove default handler

        # Add console handler with appropriate level
        logger.add(
            sys.stderr,
            level=settings.log_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
            "<level>{message}</level>",
            colorize=True,
        )

        logger.info(f"Logging configured at level: {settings.log_level}")

    def _register_handlers(self) -> None:
        """Register MCP server handlers."""

        @self.server.list_tools()
        async def handle_list_tools() -> List[Any]:
            """Handle list_tools request."""
            if not self.search_tools:
                await self._initialize_components()

            return self.search_tools.get_tools()

        @self.server.call_tool()
        async def handle_call_tool(
            name: str, arguments: Dict[str, Any]
        ) -> List[TextContent]:
            """Handle call_tool request."""
            if not self.search_tools:
                await self._initialize_components()

            logger.info(f"Tool called: {name}")

            if name == "search_bible_materials":
                return await self.search_tools.handle_search_bible_materials(arguments)
            elif name == "get_document_info":
                return await self.search_tools.handle_get_document_info(arguments)
            elif name == "get_chunk_by_id":
                return await self.search_tools.handle_get_chunk_by_id(arguments)
            else:
                raise ValueError(f"Unknown tool: {name}")

    async def _initialize_components(self) -> None:
        """Initialize RAG components lazily."""
        if self.is_initialized:
            return

        logger.info("Initializing RAG components...")

        try:
            # Initialize embedding manager
            logger.info("Loading embedding model...")
            self.embedding_manager = ChineseEmbeddingManager()

            # Initialize vector store
            logger.info("Initializing vector store...")
            self.vector_store = ChromaVectorStore(
                embedding_manager=self.embedding_manager
            )

            # Initialize reranker (optional)
            try:
                logger.info("Loading reranker model...")
                self.reranker = ChineseReranker()
            except Exception as e:
                logger.warning(f"Failed to load reranker, continuing without it: {e}")
                self.reranker = None

            # Initialize search tools
            self.search_tools = SearchTools(
                vector_store=self.vector_store,
                embedding_manager=self.embedding_manager,
                reranker=self.reranker,
            )

            # Load documents if vector store is empty
            await self._ensure_documents_loaded()

            self.is_initialized = True
            logger.info("RAG components initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize RAG components: {e}")
            raise

    async def _ensure_documents_loaded(self) -> None:
        """Ensure documents are loaded into the vector store."""
        stats = self.vector_store.get_collection_stats()

        if stats.get("document_count", 0) == 0:
            logger.info("Vector store is empty, loading documents...")
            await self._load_documents()
        else:
            logger.info(f"Vector store contains {stats['document_count']} documents")

    async def _load_documents(self) -> None:
        """Load PDF documents from the files directory."""
        files_dir = Path(settings.files_directory)

        if not files_dir.exists():
            logger.warning(f"Files directory does not exist: {files_dir}")
            return

        # Load documents
        loader = PDFDocumentLoader()
        documents = loader.load_documents_from_directory(files_dir)

        if not documents:
            logger.warning("No documents found to load")
            return

        # Chunk documents
        logger.info("Chunking documents...")
        chunker = ChineseTextChunker()
        chunks = chunker.chunk_documents(documents)

        if not chunks:
            logger.warning("No chunks created from documents")
            return

        # Add to vector store
        logger.info(f"Adding {len(chunks)} chunks to vector store...")
        self.vector_store.add_chunks(chunks)

        logger.info("Document loading completed successfully")

    async def run_stdio(self) -> None:
        """Run the server using stdio transport."""
        logger.info(f"Starting {settings.server_name} v{settings.server_version}")

        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name=settings.server_name,
                    server_version=settings.server_version,
                    capabilities=self.server.get_capabilities(
                        notification_options=None,
                        experimental_capabilities=None,
                    ),
                ),
            )


async def main() -> None:
    """Main entry point for the MCP server."""
    try:
        server = MCPServer()
        await server.run_stdio()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
