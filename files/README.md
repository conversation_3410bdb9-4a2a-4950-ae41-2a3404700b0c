# PDF Documents Directory

This directory is where you should place your Chinese-language Bible study PDF documents.

## Supported Formats

- **PDF files** (.pdf) - Primary format for document processing
- **Chinese text** - Optimized for Traditional and Simplified Chinese
- **Mixed language** - Supports documents with both Chinese and English content

## File Organization

You can organize your files in subdirectories if needed:

```
files/
├── bible-studies/
│   ├── genesis-study.pdf
│   └── exodus-study.pdf
├── commentaries/
│   ├── matthew-commentary.pdf
│   └── romans-commentary.pdf
└── devotionals/
    ├── daily-bread.pdf
    └── morning-reflections.pdf
```

## File Requirements

- **File size**: No strict limit, but larger files will take longer to process
- **Text quality**: PDFs should contain selectable text (not just images)
- **Language**: Chinese (Traditional/Simplified) and English are fully supported
- **Encoding**: UTF-8 encoding is recommended

## Processing Notes

When you start the server, it will:

1. **Scan this directory** for all PDF files (including subdirectories)
2. **Extract text** from each PDF page
3. **Detect language** automatically
4. **Chunk the text** into manageable segments
5. **Generate embeddings** for semantic search
6. **Store in vector database** for fast retrieval

## Example Files

To test the system, you can add sample PDF files such as:

- Chinese Bible study materials
- Theological commentaries in Chinese
- Devotional books
- Sermon transcripts
- Bible study guides

## Troubleshooting

If you encounter issues with specific PDF files:

1. **Check file integrity**: Ensure the PDF opens correctly in a PDF viewer
2. **Verify text content**: Make sure the PDF contains selectable text
3. **Check encoding**: Ensure Chinese characters display correctly
4. **File permissions**: Ensure the server has read access to the files

## Performance Tips

- **Smaller files process faster**: Consider splitting very large documents
- **Clear text is better**: High-quality PDFs with clear text work best
- **Consistent formatting**: Well-formatted documents chunk more effectively

## Getting Started

1. Add your PDF files to this directory
2. Start the server: `poetry run python -m triple3_mcp.server`
3. The server will automatically process all files on startup
4. Use the MCP tools to search your documents

For more information, see the main README.md file in the project root.
