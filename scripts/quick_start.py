#!/usr/bin/env python3
"""
Quick start script for Triple3 MCP Server.

This script provides a simple menu to help users get started with the
Triple3 MCP Server for Chinese Bible study materials.
"""

import subprocess
import sys
from pathlib import Path


def print_banner():
    """Print the welcome banner."""
    print("🎯 Triple3 MCP Server - Quick Start")
    print("=" * 50)
    print("Retrieval-Augmented Generation for Chinese Bible Study Materials")
    print("=" * 50)


def print_menu():
    """Print the main menu."""
    print("\n📋 What would you like to do?")
    print()
    print("1. 🎮 Run Interactive Demo")
    print("   - See RAG functionality in action")
    print("   - Test search with example queries")
    print("   - View document processing pipeline")
    print()
    print("2. 🖥️  Start MCP Server")
    print("   - Launch server for MCP client connections")
    print("   - Connect with Claude Desktop or other clients")
    print("   - Use search tools in conversations")
    print()
    print("3. 🔧 Setup Development Environment")
    print("   - Install dependencies and create directories")
    print("   - Run initial tests and checks")
    print("   - Prepare for development")
    print()
    print("4. 🧪 Run Basic Tests")
    print("   - Test core functionality")
    print("   - Verify component imports")
    print("   - Check configuration")
    print()
    print("5. 📚 View Documentation")
    print("   - Open README files")
    print("   - Show usage examples")
    print("   - Display help information")
    print()
    print("6. 🚪 Exit")
    print()


def run_command(command, description):
    """Run a command and show the result."""
    print(f"\n🔄 {description}...")
    print("-" * 40)
    
    try:
        result = subprocess.run(command, cwd=Path.cwd(), shell=True)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully!")
        else:
            print(f"❌ {description} failed with return code {result.returncode}")
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running {description}: {e}")
        return False


def show_documentation():
    """Show documentation and help."""
    print("\n📚 Documentation and Help")
    print("=" * 30)
    
    print("\n📖 Main README:")
    print("  - Project overview and features")
    print("  - Installation and setup instructions")
    print("  - Usage examples and configuration")
    print("  - File: README.md")
    
    print("\n📁 Files Directory README:")
    print("  - How to organize PDF documents")
    print("  - Supported formats and requirements")
    print("  - File: files/README.md")
    
    print("\n🔧 Scripts README:")
    print("  - Available scripts and their usage")
    print("  - Development and testing tools")
    print("  - File: scripts/README.md")
    
    print("\n💡 Quick Commands:")
    print("  poetry run python scripts/demo_rag.py          # Run demo")
    print("  poetry run python scripts/start_server.py      # Start server")
    print("  poetry run python -m triple3_mcp.server        # Alternative server start")
    print("  poetry run pytest                              # Run tests")
    
    print("\n🔗 For more help:")
    print("  - Check the main README.md file")
    print("  - Review the scripts/README.md for script details")
    print("  - Look at files/README.md for document organization")


def check_prerequisites():
    """Check if basic prerequisites are met."""
    print("\n🔍 Checking Prerequisites...")
    
    # Check if we're in the right directory
    if not Path("pyproject.toml").exists():
        print("❌ Please run this script from the project root directory")
        return False
    
    # Check if Poetry is available
    try:
        result = subprocess.run(["poetry", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Poetry found: {result.stdout.strip()}")
        else:
            print("❌ Poetry not found. Please install Poetry first.")
            return False
    except FileNotFoundError:
        print("❌ Poetry not found. Please install Poetry first.")
        return False
    
    # Check files directory
    files_dir = Path("files")
    if not files_dir.exists():
        print("📁 Creating files directory...")
        files_dir.mkdir(exist_ok=True)
    
    pdf_files = list(files_dir.glob("*.pdf"))
    if pdf_files:
        print(f"📚 Found {len(pdf_files)} PDF file(s) in files/")
    else:
        print("⚠️  No PDF files found in files/ directory")
        print("   Add PDF documents to files/ for full functionality")
    
    print("✅ Prerequisites check completed")
    return True


def main():
    """Main interactive menu."""
    print_banner()
    
    if not check_prerequisites():
        print("\n❌ Prerequisites check failed. Please fix the issues above.")
        sys.exit(1)
    
    while True:
        print_menu()
        
        try:
            choice = input("Enter your choice (1-6): ").strip()
            
            if choice == "1":
                success = run_command(
                    "poetry run python scripts/demo_rag.py",
                    "Running Interactive Demo"
                )
                if success:
                    print("\n💡 The demo showed how RAG works with your documents!")
                    print("   Next: Try starting the server (option 2) to use with MCP clients")
                
            elif choice == "2":
                print("\n🖥️  Starting MCP Server...")
                print("   The server will run until you press Ctrl+C")
                print("   Connect your MCP client (like Claude Desktop) to use the search tools")
                print()
                run_command(
                    "poetry run python scripts/start_server.py",
                    "Starting MCP Server"
                )
                
            elif choice == "3":
                success = run_command(
                    "poetry run python scripts/setup.py",
                    "Setting up Development Environment"
                )
                if success:
                    print("\n💡 Development environment is ready!")
                    print("   You can now run demos, tests, and start the server")
                
            elif choice == "4":
                success = run_command(
                    "poetry run python scripts/test_basic_functionality.py",
                    "Running Basic Tests"
                )
                if success:
                    print("\n💡 All basic tests passed!")
                    print("   Your installation is working correctly")
                
            elif choice == "5":
                show_documentation()
                
            elif choice == "6":
                print("\n👋 Thanks for using Triple3 MCP Server!")
                print("   Happy studying! 📚")
                break
                
            else:
                print("\n❌ Invalid choice. Please enter 1-6.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except EOFError:
            print("\n\n👋 Goodbye!")
            break
        
        # Wait for user to continue
        if choice in ["1", "2", "3", "4"]:
            input("\nPress Enter to continue...")


if __name__ == "__main__":
    main()
