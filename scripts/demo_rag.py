#!/usr/bin/env python3
"""
Demo script for Triple3 MCP Server RAG functionality.

This script demonstrates:
1. Starting the MCP server components
2. Loading and processing PDF documents
3. Running example RAG queries
4. Showcasing search capabilities with Chinese and English text
"""

import asyncio
import json
import sys
import time
from pathlib import Path
from typing import Optional

# Add src to path for development
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from loguru import logger
from triple3_mcp.config import settings
from triple3_mcp.rag import (
    ChineseEmbeddingManager,
    ChineseReranker,
    ChineseTextChunker,
    ChromaVectorStore,
    PDFDocumentLoader,
)
from triple3_mcp.tools import SearchTools


class RAGDemo:
    """Demo class for showcasing RAG functionality."""

    def __init__(self):
        """Initialize the RAG demo."""
        self.vector_store: Optional[ChromaVectorStore] = None
        self.embedding_manager: Optional[ChineseEmbeddingManager] = None
        self.reranker: Optional[ChineseReranker] = None
        self.search_tools: Optional[SearchTools] = None
        self.is_initialized = False

    async def initialize_components(self):
        """Initialize all RAG components."""
        print("🚀 Initializing Triple3 MCP Server RAG Components...")
        print("=" * 60)

        try:
            # Initialize embedding manager
            print("📊 Loading embedding model...")
            self.embedding_manager = ChineseEmbeddingManager(
                model_name=settings.embedding_model_name,
                cache_dir=settings.cache_directory,
            )
            print(f"✅ Embedding model loaded: {settings.embedding_model_name}")

            # Initialize vector store
            print("🗄️  Initializing vector database...")
            self.vector_store = ChromaVectorStore(
                collection_name=settings.collection_name,
                persist_directory=settings.vector_db_path,
                embedding_manager=self.embedding_manager,
            )
            print(f"✅ Vector store initialized at: {settings.vector_db_path}")

            # Try to initialize reranker (optional)
            print("🔄 Loading reranker model...")
            try:
                self.reranker = ChineseReranker(
                    model_name=settings.reranker_model_name,
                )
                print(f"✅ Reranker loaded: {settings.reranker_model_name}")
            except Exception as e:
                print(f"⚠️  Reranker not available: {e}")
                self.reranker = None

            # Initialize search tools
            self.search_tools = SearchTools(
                vector_store=self.vector_store,
                embedding_manager=self.embedding_manager,
                reranker=self.reranker,
            )
            print("✅ Search tools initialized")

            self.is_initialized = True
            print("\n🎉 All components initialized successfully!")

        except Exception as e:
            print(f"❌ Initialization failed: {e}")
            raise

    async def load_documents(self):
        """Load and process PDF documents."""
        print("\n📚 Loading PDF Documents...")
        print("=" * 40)

        files_dir = Path(settings.files_directory)
        if not files_dir.exists():
            print(f"❌ Files directory not found: {files_dir}")
            return

        # Check for PDF files
        pdf_files = list(files_dir.glob("**/*.pdf"))
        if not pdf_files:
            print(f"⚠️  No PDF files found in {files_dir}")
            print("Please add some PDF files to the files/ directory")
            return

        print(f"📄 Found {len(pdf_files)} PDF file(s):")
        for pdf_file in pdf_files:
            print(f"  - {pdf_file.name}")

        # Load documents
        print("\n🔄 Processing documents...")
        loader = PDFDocumentLoader()
        documents = loader.load_documents_from_directory(files_dir)
        print(f"✅ Loaded {len(documents)} document pages")

        # Chunk documents
        print("✂️  Chunking documents...")
        chunker = ChineseTextChunker(
            chunk_size=settings.chunk_size,
            chunk_overlap=settings.chunk_overlap,
            min_chunk_size=settings.min_chunk_size,
        )
        chunks = chunker.chunk_documents(documents)
        print(f"✅ Created {len(chunks)} text chunks")

        # Add to vector store
        print("💾 Adding chunks to vector database...")
        if self.vector_store is None:
            raise RuntimeError("Vector store not initialized")
        self.vector_store.add_chunks(chunks)
        print("✅ Documents loaded and indexed successfully!")

    async def run_example_queries(self):
        """Run example RAG queries to demonstrate functionality."""
        print("\n🔍 Running Example RAG Queries...")
        print("=" * 50)

        if self.search_tools is None:
            raise RuntimeError("Search tools not initialized")

        # Example queries in both Chinese and English
        example_queries = [
            {
                "query": "算法",
                "description": "Search for algorithm-related content (Chinese)",
                "limit": 3,
            },
            {
                "query": "data structures",
                "description": "Search for data structure content (English)",
                "limit": 3,
            },
            {
                "query": "面试",
                "description": "Search for interview-related content (Chinese)",
                "limit": 2,
            },
            {
                "query": "coding interview",
                "description": "Search for coding interview content (English)",
                "limit": 2,
            },
        ]

        for i, query_info in enumerate(example_queries, 1):
            print(f"\n📝 Query {i}: {query_info['description']}")
            print(f"🔎 Search term: '{query_info['query']}'")
            print("-" * 40)

            try:
                # Prepare search arguments
                search_args = {
                    "query": query_info["query"],
                    "limit": query_info["limit"],
                    "use_reranking": True,
                    "similarity_threshold": 0.1,
                }

                # Execute search
                start_time = time.time()
                results = await self.search_tools.handle_search_bible_materials(search_args)
                search_time = (time.time() - start_time) * 1000

                # Parse and display results
                if results and results[0].text:
                    response_data = json.loads(results[0].text)

                    print(f"⏱️  Search completed in {search_time:.2f}ms")
                    print(f"📊 Found {response_data['total_found']} results")
                    print(f"🔄 Reranking used: {response_data.get('used_reranking', False)}")

                    for j, result in enumerate(response_data['results'], 1):
                        print(f"\n  Result {j}:")
                        print(f"    📄 Source: {result['source_document']}")
                        print(f"    📃 Page: {result['page_number']}")
                        print(f"    🎯 Score: {result['similarity_score']:.3f}")
                        print(f"    📝 Text: {result['text'][:100]}...")
                        if result.get('rerank_score'):
                            print(f"    🔄 Rerank Score: {result['rerank_score']:.3f}")

                else:
                    print("❌ No results returned")

            except Exception as e:
                print(f"❌ Query failed: {e}")

    async def show_document_info(self):
        """Display information about loaded documents."""
        print("\n📊 Document Information...")
        print("=" * 30)

        if self.search_tools is None:
            raise RuntimeError("Search tools not initialized")

        try:
            results = await self.search_tools.handle_get_document_info({})

            if results and results[0].text:
                doc_info = json.loads(results[0].text)

                print(f"📚 Total documents: {doc_info['total_documents']}")
                print(f"📄 Total chunks: {doc_info['collection_stats']['document_count']}")
                print(f"🤖 Embedding model: {doc_info['embedding_model']['model_name']}")
                print(f"📐 Embedding dimension: {doc_info['embedding_model']['embedding_dimension']}")
                print(f"🔄 Reranker available: {doc_info['reranker_available']}")

                print("\n📋 Available documents:")
                for doc in doc_info['available_documents']:
                    print(f"  - {doc}")

        except Exception as e:
            print(f"❌ Failed to get document info: {e}")

    async def demonstrate_chunk_retrieval(self):
        """Demonstrate retrieving specific chunks by ID."""
        print("\n🔗 Chunk Retrieval Demo...")
        print("=" * 30)

        if self.search_tools is None:
            raise RuntimeError("Search tools not initialized")

        try:
            # First, get some chunk IDs from a search
            search_args = {"query": "algorithm", "limit": 1}
            results = await self.search_tools.handle_search_bible_materials(search_args)

            if results and results[0].text:
                response_data = json.loads(results[0].text)

                if response_data['results']:
                    chunk_id = response_data['results'][0]['chunk_id']
                    print(f"🔍 Retrieving chunk: {chunk_id}")

                    # Retrieve the specific chunk
                    chunk_args = {"chunk_id": chunk_id}
                    chunk_results = await self.search_tools.handle_get_chunk_by_id(chunk_args)

                    if chunk_results and chunk_results[0].text:
                        chunk_data = json.loads(chunk_results[0].text)
                        print(f"✅ Retrieved chunk successfully")
                        print(f"📄 Source: {chunk_data['source_document']}")
                        print(f"📃 Page: {chunk_data['page_number']}")
                        print(f"📝 Text: {chunk_data['text'][:200]}...")
                    else:
                        print("❌ Failed to retrieve chunk")
                else:
                    print("⚠️  No chunks found to demonstrate retrieval")
            else:
                print("❌ Failed to get chunk IDs")

        except Exception as e:
            print(f"❌ Chunk retrieval demo failed: {e}")

    async def run_demo(self):
        """Run the complete RAG demo."""
        print("🎯 Triple3 MCP Server RAG Demo")
        print("=" * 60)
        print("This demo showcases the Retrieval-Augmented Generation capabilities")
        print("of the Triple3 MCP Server for Chinese Bible study materials.")
        print()

        try:
            # Initialize components
            await self.initialize_components()

            # Load documents
            await self.load_documents()

            # Show document information
            await self.show_document_info()

            # Run example queries
            await self.run_example_queries()

            # Demonstrate chunk retrieval
            await self.demonstrate_chunk_retrieval()

            print("\n" + "=" * 60)
            print("🎉 RAG Demo completed successfully!")
            print("\n💡 Next steps:")
            print("1. Add more PDF documents to the files/ directory")
            print("2. Start the MCP server: poetry run python -m triple3_mcp.server")
            print("3. Connect the server to an MCP client (like Claude Desktop)")
            print("4. Use the search tools in your conversations!")

        except Exception as e:
            print(f"\n❌ Demo failed: {e}")
            import traceback
            traceback.print_exc()


async def main():
    """Main entry point for the RAG demo."""
    demo = RAGDemo()
    await demo.run_demo()


if __name__ == "__main__":
    # Configure logging for demo
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | {message}",
        colorize=True,
    )

    asyncio.run(main())
