# Scripts Directory

This directory contains utility scripts for the Triple3 MCP Server.

## Available Scripts

### 🚀 `quick_start.py` - Interactive Quick Start Menu
Interactive menu to help users get started with the Triple3 MCP Server.

```bash
python scripts/quick_start.py
```

**What it does:**
- Provides an interactive menu with all available options
- Checks prerequisites and environment setup
- Guides users through demos, server startup, and testing
- Shows documentation and help information
- Perfect for first-time users and quick access to all features

### 🎮 `demo_rag.py` - RAG Functionality Demo
Demonstrates the complete RAG pipeline with example queries.

```bash
python scripts/demo_rag.py
```

**What it does:**
- Initializes all RAG components (embeddings, vector store, reranker)
- Loads and processes PDF documents from `files/` directory
- Runs example search queries in Chinese and English
- Shows document statistics and chunk retrieval capabilities
- Provides detailed output of the RAG process

### 🖥️ `start_server.py` - MCP Server Launcher
Starts the MCP server for client connections.

```bash
python scripts/start_server.py
```

**What it does:**
- Checks prerequisites and creates necessary directories
- Displays server configuration
- Starts the MCP server listening on stdio
- Processes documents and builds vector database
- Waits for MCP client connections

### 🎯 `run_demo.py` - Unified Demo Runner
Combines both demo and server functionality with command-line options.

```bash
# Run interactive demo
python scripts/run_demo.py demo

# Start MCP server
python scripts/run_demo.py server

# Show help
python scripts/run_demo.py --help
```

### 🔧 `setup.py` - Development Environment Setup
Sets up the development environment with dependencies and directories.

```bash
python scripts/setup.py
```

### 🧪 `test_basic_functionality.py` - Basic Tests
Tests basic functionality of core components.

```bash
python scripts/test_basic_functionality.py
```

### 🏃 `run_server.py` - Simple Server Runner
Basic server runner (alternative to using the module directly).

```bash
python scripts/run_server.py
```

## Quick Start Guide

### 🚀 Easiest Way (Recommended)
```bash
python scripts/quick_start.py
```
This interactive menu will guide you through all options!

### 📋 Manual Steps

1. **First-time setup:**
   ```bash
   python scripts/setup.py
   ```

2. **Add PDF documents:**
   ```bash
   cp your-documents.pdf files/
   ```

3. **Run the demo to see RAG in action:**
   ```bash
   python scripts/demo_rag.py
   # OR
   python scripts/run_demo.py demo
   ```

4. **Start the server for MCP client connections:**
   ```bash
   python scripts/start_server.py
   # OR
   python scripts/run_demo.py server
   ```

## Script Dependencies

All scripts automatically add the `src/` directory to the Python path, so they can be run directly without installing the package.

Required dependencies are managed through Poetry and should be installed via:
```bash
poetry install
```

## Environment Variables

Scripts respect the same environment variables as the main application:
- `LOG_LEVEL` - Logging level (default: INFO)
- `FILES_DIRECTORY` - PDF documents directory (default: files)
- `VECTOR_DB_PATH` - Vector database path (default: data/vector_db)

## Troubleshooting

### Common Issues

1. **Import errors:** Make sure you're running from the project root directory
2. **No PDF files:** Add PDF documents to the `files/` directory
3. **Permission errors:** Ensure the scripts are executable (`chmod +x scripts/*.py`)
4. **Missing dependencies:** Run `poetry install` to install required packages

### Getting Help

- Run any script with `--help` for usage information
- Check the main README.md for detailed setup instructions
- Review the logs for detailed error information

## Development

When developing new scripts:
1. Add the src path setup at the top
2. Use the same logging configuration pattern
3. Include proper error handling and user feedback
4. Add the script to this README with description
