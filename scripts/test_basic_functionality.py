#!/usr/bin/env python3
"""Test script to verify basic functionality of Triple3 MCP Server components."""

import sys
from pathlib import Path

# Add src to path for development
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from triple3_mcp.config import settings
from triple3_mcp.rag.chunking import ChineseTextChunker, TextChunk
from triple3_mcp.rag.document_loader import DocumentMetadata, DocumentPage


def test_config():
    """Test configuration loading."""
    print("🔧 Testing configuration...")
    print(f"  Server name: {settings.server_name}")
    print(f"  Files directory: {settings.files_directory}")
    print(f"  Vector DB path: {settings.vector_db_path}")
    print(f"  Embedding model: {settings.embedding_model_name}")
    print("✅ Configuration loaded successfully")


def test_chunking():
    """Test text chunking functionality."""
    print("\n📝 Testing text chunking...")
    
    # Create sample document
    metadata = DocumentMetadata(
        filename="test.pdf",
        file_path="/test/test.pdf",
        page_count=1,
        language="zh",
        file_size=1000
    )
    
    sample_text = """
    这是一个测试文档，用于验证中文文本分块功能。
    
    第一段：信心是什么？信心是对所盼望之事的实底，是未见之事的确据。
    
    第二段：爱是恒久忍耐，又有恩慈；爱是不嫉妒，爱是不自夸，不张狂。
    
    第三段：我们晓得万事都互相效力，叫爱神的人得益处。
    """
    
    document = DocumentPage(
        page_number=1,
        text_content=sample_text,
        metadata=metadata
    )
    
    # Test chunking
    chunker = ChineseTextChunker(chunk_size=100, chunk_overlap=20, min_chunk_size=30)
    chunks = chunker.chunk_document(document)
    
    print(f"  Created {len(chunks)} chunks from sample text")
    for i, chunk in enumerate(chunks):
        print(f"  Chunk {i+1}: {chunk.text[:50]}...")
        print(f"    Word count: {chunk.word_count}")
    
    print("✅ Text chunking working correctly")


def test_imports():
    """Test that all major components can be imported."""
    print("\n📦 Testing imports...")
    
    try:
        from triple3_mcp.rag import (
            ChineseTextChunker,
            PDFDocumentLoader,
            ChineseEmbeddingManager,
            ChromaVectorStore,
        )
        print("  ✅ RAG components imported successfully")
    except ImportError as e:
        print(f"  ❌ RAG import failed: {e}")
        return False
    
    try:
        from triple3_mcp.tools import SearchTools
        print("  ✅ Search tools imported successfully")
    except ImportError as e:
        print(f"  ❌ Search tools import failed: {e}")
        return False
    
    try:
        from triple3_mcp.server import MCPServer
        print("  ✅ MCP server imported successfully")
    except ImportError as e:
        print(f"  ❌ MCP server import failed: {e}")
        return False
    
    return True


def main():
    """Run all basic functionality tests."""
    print("🚀 Testing Triple3 MCP Server Basic Functionality")
    print("=" * 60)
    
    try:
        test_config()
        
        if not test_imports():
            print("\n❌ Import tests failed")
            return False
        
        test_chunking()
        
        print("\n" + "=" * 60)
        print("🎉 All basic functionality tests passed!")
        print("\nNext steps:")
        print("1. Add PDF documents to the 'files/' directory")
        print("2. Run the full test suite: poetry run pytest")
        print("3. Start the MCP server: poetry run python -m triple3_mcp.server")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
