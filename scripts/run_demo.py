#!/usr/bin/env python3
"""
Comprehensive script to run Triple3 MCP Server demos and start the server.

Usage:
    python scripts/run_demo.py demo     # Run RAG functionality demo
    python scripts/run_demo.py server   # Start the MCP server
    python scripts/run_demo.py --help   # Show help
"""

import argparse
import asyncio
import subprocess
import sys
from pathlib import Path


def create_parser():
    """Create command line argument parser."""
    parser = argparse.ArgumentParser(
        description="Triple3 MCP Server Demo and Server Launcher",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python scripts/run_demo.py demo     # Run interactive RAG demo
  python scripts/run_demo.py server   # Start MCP server for client connections

The demo mode will:
- Initialize all RAG components
- Load and process PDF documents from files/
- Run example search queries in Chinese and English
- Show document statistics and chunk retrieval

The server mode will:
- Start the MCP server listening on stdio
- Process documents and build vector database
- Wait for MCP client connections (like <PERSON>)
        """
    )

    parser.add_argument(
        "mode",
        choices=["demo", "server"],
        help="Mode to run: 'demo' for interactive demonstration, 'server' to start MCP server"
    )

    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )

    return parser


def print_welcome():
    """Print welcome message."""
    print("🎯 Triple3 MCP Server")
    print("=" * 50)
    print("Retrieval-Augmented Generation for Chinese Bible Study Materials")
    print("=" * 50)


def run_demo_mode():
    """Run the interactive RAG demo."""
    print("🎮 Running Interactive RAG Demo...")
    print()
    print("This demo will showcase the RAG capabilities by:")
    print("- Loading and processing PDF documents")
    print("- Running semantic search queries")
    print("- Demonstrating multilingual support (Chinese/English)")
    print("- Showing document statistics and chunk retrieval")
    print()

    # Run the demo script
    result = subprocess.run([
        sys.executable, "scripts/demo_rag.py"
    ], cwd=Path.cwd())

    return result.returncode == 0


def run_server_mode():
    """Run the MCP server."""
    print("🖥️  Starting MCP Server Mode...")
    print()
    print("The server will:")
    print("- Process PDF documents from files/ directory")
    print("- Build vector database for semantic search")
    print("- Listen for MCP client connections via stdio")
    print("- Provide search tools for connected clients")
    print()

    # Run the server script
    result = subprocess.run([
        sys.executable, "scripts/start_server.py"
    ], cwd=Path.cwd())

    return result.returncode == 0


def check_environment():
    """Check if the environment is properly set up."""
    print("🔍 Checking environment...")

    # Check if we're in the right directory
    if not Path("pyproject.toml").exists():
        print("❌ Please run this script from the project root directory")
        print("   cd /path/to/triple3-mcp && python scripts/run_demo.py")
        sys.exit(1)

    # Check if files directory exists
    files_dir = Path("files")
    if not files_dir.exists():
        print("📁 Creating files directory...")
        files_dir.mkdir(exist_ok=True)

    print("✅ Environment check passed")


def main():
    """Main entry point."""
    parser = create_parser()
    args = parser.parse_args()

    print_welcome()
    check_environment()

    try:
        if args.mode == "demo":
            success = run_demo_mode()
        elif args.mode == "server":
            success = run_server_mode()
        else:
            print(f"❌ Unknown mode: {args.mode}")
            sys.exit(1)

        if not success:
            print(f"\n❌ {args.mode.title()} mode failed")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n👋 Stopped by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
