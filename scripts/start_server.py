#!/usr/bin/env python3
"""
Simple script to start the Triple3 MCP Server.

This script provides an easy way to start the MCP server with proper
environment setup and clear status messages.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for development
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from loguru import logger
from triple3_mcp.config import settings
from triple3_mcp.server import main as server_main


def check_prerequisites():
    """Check if prerequisites are met before starting the server."""
    print("🔍 Checking prerequisites...")
    
    # Check if files directory exists
    files_dir = Path(settings.files_directory)
    if not files_dir.exists():
        print(f"❌ Files directory not found: {files_dir}")
        print("Creating files directory...")
        files_dir.mkdir(parents=True, exist_ok=True)
        print(f"✅ Created files directory: {files_dir}")
    
    # Check for PDF files
    pdf_files = list(files_dir.glob("**/*.pdf"))
    if not pdf_files:
        print(f"⚠️  No PDF files found in {files_dir}")
        print("The server will start, but you should add PDF files for full functionality.")
        print("See files/README.md for guidance on organizing your documents.")
    else:
        print(f"📚 Found {len(pdf_files)} PDF file(s):")
        for pdf_file in pdf_files:
            print(f"  - {pdf_file.name}")
    
    # Check data directories
    data_dirs = [settings.vector_db_path.parent, settings.cache_directory]
    for data_dir in data_dirs:
        if not data_dir.exists():
            print(f"📁 Creating data directory: {data_dir}")
            data_dir.mkdir(parents=True, exist_ok=True)
    
    print("✅ Prerequisites check completed")


def print_server_info():
    """Print server configuration information."""
    print("\n📋 Server Configuration:")
    print(f"  Server Name: {settings.server_name}")
    print(f"  Version: {settings.server_version}")
    print(f"  Files Directory: {settings.files_directory}")
    print(f"  Vector DB Path: {settings.vector_db_path}")
    print(f"  Embedding Model: {settings.embedding_model_name}")
    print(f"  Reranker Model: {settings.reranker_model_name}")
    print(f"  Log Level: {settings.log_level}")


def print_usage_instructions():
    """Print instructions for using the server."""
    print("\n📖 Usage Instructions:")
    print("1. The server will start and listen for MCP connections via stdio")
    print("2. Add PDF documents to the files/ directory")
    print("3. Connect an MCP client (like Claude Desktop) to use the server")
    print("4. Available tools:")
    print("   - search_bible_materials: Semantic search across documents")
    print("   - get_document_info: Get information about loaded documents")
    print("   - get_chunk_by_id: Retrieve specific text chunks")
    print("\n🔗 For MCP client configuration, see the main README.md")


async def start_server():
    """Start the MCP server with proper setup."""
    print("🚀 Starting Triple3 MCP Server")
    print("=" * 50)
    
    # Check prerequisites
    check_prerequisites()
    
    # Print server info
    print_server_info()
    
    # Print usage instructions
    print_usage_instructions()
    
    print("\n" + "=" * 50)
    print("🎯 Server starting...")
    print("Press Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        # Start the server
        await server_main()
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Configure logging for startup
    logger.remove()
    logger.add(
        sys.stderr,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
        "<level>{message}</level>",
        colorize=True,
    )
    
    asyncio.run(start_server())
