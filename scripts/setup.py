#!/usr/bin/env python3
"""Setup script for Triple3 MCP Server development environment."""

import subprocess
import sys
from pathlib import Path


def run_command(command: str, check: bool = True) -> subprocess.CompletedProcess:
    """Run a shell command and return the result."""
    print(f"Running: {command}")
    result = subprocess.run(command, shell=True, check=check)
    return result


def setup_development_environment():
    """Set up the development environment."""
    print("Setting up Triple3 MCP Server development environment...")
    
    # Check if Poetry is installed
    try:
        run_command("poetry --version")
    except subprocess.CalledProcessError:
        print("Poetry is not installed. Please install Poetry first:")
        print("curl -sSL https://install.python-poetry.org | python3 -")
        sys.exit(1)
    
    # Install dependencies
    print("\n1. Installing dependencies...")
    run_command("poetry install")
    
    # Install pre-commit hooks
    print("\n2. Installing pre-commit hooks...")
    run_command("poetry run pre-commit install")
    
    # Create necessary directories
    print("\n3. Creating necessary directories...")
    directories = [
        "files",
        "data/vector_db",
        "data/cache",
        "tests/fixtures/sample_documents"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"Created directory: {directory}")
    
    # Run initial code quality checks
    print("\n4. Running initial code quality checks...")
    run_command("poetry run black --check src/ tests/", check=False)
    run_command("poetry run isort --check-only src/ tests/", check=False)
    run_command("poetry run flake8 src/ tests/", check=False)
    
    print("\n✅ Development environment setup complete!")
    print("\nNext steps:")
    print("1. Add PDF documents to the 'files/' directory")
    print("2. Run tests: poetry run pytest")
    print("3. Start the server: poetry run python -m triple3_mcp.server")
    print("4. Format code: poetry run black src/ tests/")
    print("5. Sort imports: poetry run isort src/ tests/")


if __name__ == "__main__":
    setup_development_environment()
