# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Testing
.coverage
.pytest_cache/
.tox/
.nox/
htmlcov/
.hypothesis/

# Documentation
docs/
*.md
!README.md

# Development
.pre-commit-config.yaml
tests/
scripts/

# Data (exclude from image, mount as volume)
data/
*.db
*.sqlite

# Logs
*.log

# Temporary files
tmp/
temp/
