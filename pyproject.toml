[tool.poetry]
name = "triple3-mcp"
version = "0.1.0"
description = "MCP server for Chinese-language Bible study materials with RAG capabilities"
authors = ["<PERSON><PERSON><PERSON> Dai <<EMAIL>>"]
readme = "README.md"
packages = [{include = "triple3_mcp", from = "src"}]

[tool.poetry.dependencies]
python = "^3.10"
mcp = "^1.0.0"
sentence-transformers = "^3.0.0"
chromadb = "^0.5.0"
pymupdf = "^1.24.0"
numpy = "^1.24.0"
pandas = "^2.0.0"
pydantic = "^2.0.0"
pydantic-settings = "^2.0.0"
loguru = "^0.7.0"
uvicorn = "^0.30.0"
fastapi = "^0.110.0"
httpx = "^0.27.0"
torch = "^2.1.0"
transformers = "^4.35.0"
jieba = "^0.42.1"
langdetect = "^1.0.9"

[tool.poetry.group.dev.dependencies]
pytest = "^8.0.0"
pytest-cov = "^5.0.0"
pytest-asyncio = "^0.23.0"
black = "^24.0.0"
isort = "^5.13.0"
flake8 = "^7.0.0"
mypy = "^1.8.0"
pre-commit = "^3.6.0"
pytest-mock = "^3.12.0"
httpx = "^0.27.0"

[tool.poetry.scripts]
triple3-mcp = "triple3_mcp.server:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["triple3_mcp"]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "chromadb.*",
    "sentence_transformers.*",
    "transformers.*",
    "jieba.*",
    "langdetect.*",
    "fitz.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--cov=src/triple3_mcp",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-fail-under=80",
    "-v"
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
