"""Tests for the main MCP server."""

from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch

import pytest

from triple3_mcp.config import settings
from triple3_mcp.server import MCPServer


class TestMCPServer:
    """Test cases for MCPServer class."""

    @pytest.fixture
    def server(self):
        """Create a test server instance."""
        return MCPServer()

    def test_server_initialization(self, server):
        """Test server initialization."""
        assert server.server is not None
        assert server.server.name == settings.server_name
        assert not server.is_initialized
        assert server.search_tools is None

    @pytest.mark.asyncio
    async def test_initialize_components(self, server):
        """Test component initialization."""
        with (
            patch("triple3_mcp.server.ChineseEmbeddingManager") as mock_embedding,
            patch("triple3_mcp.server.ChromaVectorStore") as mock_vector_store,
            patch("triple3_mcp.server.ChineseReranker") as mock_reranker,
            patch("triple3_mcp.server.SearchTools") as mock_search_tools,
            patch.object(server, "_ensure_documents_loaded", new_callable=AsyncMock),
        ):

            # Mock the components
            mock_embedding.return_value = MagicMock()
            mock_vector_store.return_value = MagicMock()
            mock_reranker.return_value = MagicMock()
            mock_search_tools.return_value = MagicMock()

            await server._initialize_components()

            assert server.is_initialized
            assert server.embedding_manager is not None
            assert server.vector_store is not None
            assert server.search_tools is not None

    @pytest.mark.asyncio
    async def test_initialize_components_without_reranker(self, server):
        """Test component initialization when reranker fails to load."""
        with (
            patch("triple3_mcp.server.ChineseEmbeddingManager") as mock_embedding,
            patch("triple3_mcp.server.ChromaVectorStore") as mock_vector_store,
            patch(
                "triple3_mcp.server.ChineseReranker",
                side_effect=Exception("Reranker failed"),
            ),
            patch("triple3_mcp.server.SearchTools") as mock_search_tools,
            patch.object(server, "_ensure_documents_loaded", new_callable=AsyncMock),
        ):

            # Mock the components
            mock_embedding.return_value = MagicMock()
            mock_vector_store.return_value = MagicMock()
            mock_search_tools.return_value = MagicMock()

            await server._initialize_components()

            assert server.is_initialized
            assert server.reranker is None  # Should be None due to failure

    @pytest.mark.asyncio
    async def test_ensure_documents_loaded_empty_store(self, server):
        """Test document loading when vector store is empty."""
        server.vector_store = MagicMock()
        server.vector_store.get_collection_stats.return_value = {"document_count": 0}

        with patch.object(
            server, "_load_documents", new_callable=AsyncMock
        ) as mock_load:
            await server._ensure_documents_loaded()
            mock_load.assert_called_once()

    @pytest.mark.asyncio
    async def test_ensure_documents_loaded_populated_store(self, server):
        """Test document loading when vector store has documents."""
        server.vector_store = MagicMock()
        server.vector_store.get_collection_stats.return_value = {"document_count": 10}

        with patch.object(
            server, "_load_documents", new_callable=AsyncMock
        ) as mock_load:
            await server._ensure_documents_loaded()
            mock_load.assert_not_called()

    @pytest.mark.asyncio
    async def test_load_documents(self, server):
        """Test document loading process."""
        server.vector_store = MagicMock()

        # Mock document loader
        mock_documents = [MagicMock()]
        mock_chunks = [MagicMock()]

        with (
            patch("triple3_mcp.server.PDFDocumentLoader") as mock_loader_class,
            patch("triple3_mcp.server.ChineseTextChunker") as mock_chunker_class,
            patch("pathlib.Path.exists", return_value=True),
        ):

            mock_loader = MagicMock()
            mock_loader.load_documents_from_directory.return_value = mock_documents
            mock_loader_class.return_value = mock_loader

            mock_chunker = MagicMock()
            mock_chunker.chunk_documents.return_value = mock_chunks
            mock_chunker_class.return_value = mock_chunker

            await server._load_documents()

            server.vector_store.add_chunks.assert_called_once_with(mock_chunks)

    @pytest.mark.asyncio
    async def test_load_documents_no_files_directory(self, server):
        """Test document loading when files directory doesn't exist."""
        server.vector_store = MagicMock()

        with patch("pathlib.Path.exists", return_value=False):
            await server._load_documents()

            # Should not attempt to add chunks
            server.vector_store.add_chunks.assert_not_called()
