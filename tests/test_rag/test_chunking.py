"""Tests for Chinese text chunking functionality."""

from unittest.mock import <PERSON>Mock

import pytest

from triple3_mcp.rag.chunking import ChineseTextChunker, TextChunk
from triple3_mcp.rag.document_loader import DocumentMetadata, DocumentPage


class TestChineseTextChunker:
    """Test cases for ChineseTextChunker class."""

    @pytest.fixture
    def chunker(self):
        """Create a test chunker instance."""
        return ChineseTextChunker(chunk_size=100, chunk_overlap=20, min_chunk_size=30)

    @pytest.fixture
    def sample_document(self):
        """Create a sample document for testing."""
        metadata = DocumentMetadata(
            filename="test.pdf",
            file_path="/test/test.pdf",
            page_count=1,
            language="zh",
            file_size=1000,
        )

        return DocumentPage(
            page_number=1,
            text_content="这是一个测试文档。它包含中文内容。我们将测试文本分块功能。"
            * 10,
            metadata=metadata,
        )

    def test_chunker_initialization(self, chunker):
        """Test chunker initialization."""
        assert chunker.chunk_size == 100
        assert chunker.chunk_overlap == 20
        assert chunker.min_chunk_size == 30

    def test_chunk_short_document(self, chunker):
        """Test chunking a document shorter than min_chunk_size."""
        metadata = DocumentMetadata(
            filename="short.pdf",
            file_path="/test/short.pdf",
            page_count=1,
            language="zh",
            file_size=100,
        )

        short_doc = DocumentPage(
            page_number=1, text_content="短文档", metadata=metadata
        )

        chunks = chunker.chunk_document(short_doc)

        assert len(chunks) == 1
        assert chunks[0].text == "短文档"
        assert chunks[0].chunk_index == 0

    def test_chunk_long_document(self, chunker, sample_document):
        """Test chunking a long document."""
        chunks = chunker.chunk_document(sample_document)

        assert len(chunks) > 1

        # Check that all chunks have required fields
        for i, chunk in enumerate(chunks):
            assert isinstance(chunk, TextChunk)
            assert chunk.chunk_index == i
            assert chunk.source_document == "test.pdf"
            assert chunk.page_number == 1
            assert len(chunk.text) >= chunker.min_chunk_size or i == len(chunks) - 1

    def test_chunk_multiple_documents(self, chunker, sample_document):
        """Test chunking multiple documents."""
        documents = [sample_document, sample_document]
        chunks = chunker.chunk_documents(documents)

        assert len(chunks) > 0

        # Check that chunks from different documents have different IDs
        chunk_ids = [chunk.chunk_id for chunk in chunks]
        assert len(set(chunk_ids)) == len(chunk_ids)  # All IDs should be unique

    def test_word_count(self, chunker):
        """Test word counting for Chinese text."""
        chinese_text = "这是一个测试文档"
        word_count = chunker._count_words(chinese_text)

        assert word_count > 0
        assert isinstance(word_count, int)

    def test_chunk_id_generation(self, chunker, sample_document):
        """Test chunk ID generation."""
        chunk_id = chunker._generate_chunk_id(sample_document, 0)

        assert "test" in chunk_id
        assert "page1" in chunk_id
        assert "chunk0" in chunk_id

    def test_overlap_text_extraction(self, chunker):
        """Test overlap text extraction."""
        text = "这是一个很长的文本，用于测试重叠功能。"
        overlap = chunker._get_overlap_text(text)

        assert len(overlap) <= chunker.chunk_overlap
        assert overlap in text

    def test_semantic_chunking_with_paragraphs(self, chunker):
        """Test semantic chunking with paragraph breaks."""
        metadata = DocumentMetadata(
            filename="para.pdf",
            file_path="/test/para.pdf",
            page_count=1,
            language="zh",
            file_size=1000,
        )

        text_with_paragraphs = """第一段内容。这是第一段的详细内容。

第二段内容。这是第二段的详细内容。

第三段内容。这是第三段的详细内容。"""

        doc = DocumentPage(
            page_number=1, text_content=text_with_paragraphs, metadata=metadata
        )

        chunks = chunker.chunk_document(doc)

        # Should respect paragraph boundaries
        assert len(chunks) >= 1
        for chunk in chunks:
            assert len(chunk.text.strip()) > 0
