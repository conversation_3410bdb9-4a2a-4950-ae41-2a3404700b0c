"""Tests for Chinese embedding functionality."""

from unittest.mock import MagicMock, patch

import numpy as np
import pytest

from triple3_mcp.rag.chunking import TextChunk
from triple3_mcp.rag.embeddings import ChineseEmbeddingManager


class TestChineseEmbeddingManager:
    """Test cases for ChineseEmbeddingManager class."""

    @pytest.fixture
    def mock_model(self):
        """Create a mock sentence transformer model."""
        model = MagicMock()
        model.get_sentence_embedding_dimension.return_value = 384
        model.encode.return_value = np.random.rand(2, 384)
        return model

    @pytest.fixture
    def embedding_manager(self, mock_model):
        """Create a test embedding manager with mocked model."""
        with patch(
            "triple3_mcp.rag.embeddings.SentenceTransformer", return_value=mock_model
        ):
            manager = ChineseEmbeddingManager()
            return manager

    def test_initialization(self, embedding_manager):
        """Test embedding manager initialization."""
        assert embedding_manager.model is not None
        assert embedding_manager.embedding_dimension == 384
        assert embedding_manager.device in ["cpu", "cuda", "mps"]

    def test_embed_texts(self, embedding_manager):
        """Test embedding generation for multiple texts."""
        texts = ["这是测试文本", "This is test text"]
        embeddings = embedding_manager.embed_texts(texts)

        assert isinstance(embeddings, np.ndarray)
        assert embeddings.shape[0] == len(texts)
        assert embeddings.shape[1] == embedding_manager.embedding_dimension

    def test_embed_empty_texts(self, embedding_manager):
        """Test embedding generation for empty text list."""
        embeddings = embedding_manager.embed_texts([])

        assert isinstance(embeddings, np.ndarray)
        assert embeddings.shape == (0, embedding_manager.embedding_dimension)

    def test_embed_query(self, embedding_manager):
        """Test embedding generation for single query."""
        query = "什么是信心？"
        embedding = embedding_manager.embed_query(query)

        assert isinstance(embedding, np.ndarray)
        assert embedding.shape == (embedding_manager.embedding_dimension,)

    def test_embed_chunks(self, embedding_manager):
        """Test embedding generation for text chunks."""
        chunks = [
            TextChunk(
                chunk_id="test_1",
                text="测试文本一",
                source_document="test.pdf",
                page_number=1,
                chunk_index=0,
                start_char=0,
                end_char=10,
                word_count=5,
            ),
            TextChunk(
                chunk_id="test_2",
                text="测试文本二",
                source_document="test.pdf",
                page_number=1,
                chunk_index=1,
                start_char=10,
                end_char=20,
                word_count=5,
            ),
        ]

        embeddings = embedding_manager.embed_chunks(chunks)

        assert len(embeddings) == len(chunks)
        assert all(isinstance(emb, np.ndarray) for emb in embeddings)

    def test_compute_similarity(self, embedding_manager):
        """Test similarity computation."""
        query_embedding = np.random.rand(384)
        doc_embeddings = [np.random.rand(384), np.random.rand(384)]

        similarities = embedding_manager.compute_similarity(
            query_embedding, doc_embeddings
        )

        assert len(similarities) == len(doc_embeddings)
        assert all(isinstance(sim, float) for sim in similarities)

    def test_compute_similarity_empty(self, embedding_manager):
        """Test similarity computation with empty document list."""
        query_embedding = np.random.rand(384)
        similarities = embedding_manager.compute_similarity(query_embedding, [])

        assert similarities == []

    def test_get_model_info(self, embedding_manager):
        """Test model information retrieval."""
        info = embedding_manager.get_model_info()

        assert "model_name" in info
        assert "embedding_dimension" in info
        assert "device" in info
        assert info["embedding_dimension"] == 384

    def test_preprocess_text(self, embedding_manager):
        """Test text preprocessing."""
        text = "  这是一个   测试文本  "
        processed = embedding_manager.preprocess_text_for_embedding(text)

        assert processed == "这是一个 测试文本"

    def test_preprocess_long_text(self, embedding_manager):
        """Test preprocessing of very long text."""
        long_text = "测试" * 1000  # Very long text
        processed = embedding_manager.preprocess_text_for_embedding(long_text)

        # Should be truncated
        assert len(processed) <= 512

    @patch("triple3_mcp.rag.embeddings.torch.cuda.is_available", return_value=True)
    def test_get_best_device_cuda(self, mock_cuda):
        """Test device selection when CUDA is available."""
        with patch("triple3_mcp.rag.embeddings.SentenceTransformer"):
            manager = ChineseEmbeddingManager()
            device = manager._get_best_device()
            assert device == "cuda"

    @patch("triple3_mcp.rag.embeddings.torch.cuda.is_available", return_value=False)
    @patch(
        "triple3_mcp.rag.embeddings.torch.backends.mps.is_available", return_value=True
    )
    def test_get_best_device_mps(self, mock_mps, mock_cuda):
        """Test device selection when MPS is available."""
        with patch("triple3_mcp.rag.embeddings.SentenceTransformer"):
            manager = ChineseEmbeddingManager()
            device = manager._get_best_device()
            assert device == "mps"

    @patch("triple3_mcp.rag.embeddings.torch.cuda.is_available", return_value=False)
    def test_get_best_device_cpu(self, mock_cuda):
        """Test device selection fallback to CPU."""
        with patch("triple3_mcp.rag.embeddings.SentenceTransformer"):
            manager = ChineseEmbeddingManager()
            device = manager._get_best_device()
            assert device == "cpu"
