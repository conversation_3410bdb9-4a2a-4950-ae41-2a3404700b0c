# Triple3 MCP Server Configuration
# Copy this file to .env and modify as needed

# Server Configuration
SERVER_NAME=triple3-mcp
SERVER_VERSION=0.1.0
LOG_LEVEL=INFO

# File and Directory Paths
FILES_DIRECTORY=files
VECTOR_DB_PATH=data/vector_db
CACHE_DIRECTORY=data/cache

# Embedding Configuration
EMBEDDING_MODEL_NAME=sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
EMBEDDING_DIMENSION=384

# Reranking Configuration
RERANKER_MODEL_NAME=cross-encoder/ms-marco-MiniLM-L-12-v2
RERANK_TOP_K=10

# Chunking Configuration
CHUNK_SIZE=512
CHUNK_OVERLAP=50
MIN_CHUNK_SIZE=100

# Search Configuration
DEFAULT_SEARCH_LIMIT=5
MAX_SEARCH_LIMIT=20
SIMILARITY_THRESHOLD=0.7

# Vector Database Configuration
COLLECTION_NAME=chinese_bible_study
DISTANCE_METRIC=cosine

# PDF Processing Configuration
SUPPORTED_LANGUAGES=["zh", "zh-cn", "zh-tw", "en"]
EXTRACT_IMAGES=false

# Performance Configuration
BATCH_SIZE=32
MAX_WORKERS=4

# Cloud Run Configuration (for production deployment)
PORT=8080
HOST=0.0.0.0
