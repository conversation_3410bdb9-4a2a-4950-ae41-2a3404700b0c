# Triple3 MCP Server

A Model Context Protocol (MCP) server that provides Retrieval-Augmented Generation (RAG) capabilities for Chinese-language Bible study materials. This server enables semantic search and question answering over PDF documents using advanced embedding models and reranking techniques.

## Features

- **Chinese Text Support**: Optimized for Chinese-language Bible study materials with proper text segmentation
- **Semantic Search**: Uses multilingual sentence transformers for accurate semantic similarity
- **Reranking**: Improves retrieval quality with cross-encoder models
- **MCP Protocol**: Full compliance with Model Context Protocol for easy integration
- **Vector Database**: ChromaDB for efficient storage and retrieval
- **Cloud Ready**: Designed for deployment on Google Cloud Run
- **Production Quality**: Comprehensive testing, logging, and error handling

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   PDF Files     │───▶│  Document Loader │───▶│  Text Chunker   │
│   (files/)      │    │  (PyMuPDF)       │    │  (Chinese-aware)│
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   MCP Client    │◀───│   Search Tools   │◀───│  Vector Store   │
│                 │    │   (MCP Tools)    │    │  (ChromaDB)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        ▲
                                ▼                        │
                       ┌──────────────────┐    ┌─────────────────┐
                       │    Reranker      │    │  Embeddings     │
                       │ (Cross-encoder)  │    │ (Sentence-T)    │
                       └──────────────────┘    └─────────────────┘
```

## Quick Start

### Prerequisites

- Python 3.10 or higher
- Poetry for dependency management
- Git

### Installation

1. **Clone the repository:**
   ```bash
   git clone https://github.com/ExcelsisCN/triple3-mcp.git
   cd triple3-mcp
   ```

2. **Set up the development environment:**
   ```bash
   python scripts/setup.py
   ```

3. **Add PDF documents:**
   ```bash
   # Place your Chinese Bible study PDF files in the files/ directory
   cp your-study-materials.pdf files/
   ```

4. **Run the server:**
   ```bash
   poetry run python -m triple3_mcp.server
   ```

### Manual Installation

If you prefer manual setup:

```bash
# Install Poetry if not already installed
curl -sSL https://install.python-poetry.org | python3 -

# Install dependencies
poetry install

# Install pre-commit hooks
poetry run pre-commit install

# Create necessary directories
mkdir -p files data/vector_db data/cache
```

## Usage

### MCP Tools

The server provides three main MCP tools:

#### 1. `search_bible_materials`

Search for relevant passages in Chinese Bible study materials.

**Parameters:**
- `query` (required): Search query in Chinese or English
- `limit` (optional): Maximum number of results (default: 5, max: 20)
- `use_reranking` (optional): Whether to use reranking (default: true)
- `similarity_threshold` (optional): Minimum similarity score (default: 0.7)
- `source_filter` (optional): Filter by specific document name

**Example:**
```json
{
  "query": "什么是信心？",
  "limit": 10,
  "use_reranking": true,
  "similarity_threshold": 0.6
}
```

#### 2. `get_document_info`

Get information about available documents in the knowledge base.

**Parameters:** None

**Returns:** Collection statistics, available documents, and model information.

#### 3. `get_chunk_by_id`

Retrieve a specific text chunk by its unique identifier.

**Parameters:**
- `chunk_id` (required): Unique chunk identifier

### Configuration

The server can be configured through environment variables or a `.env` file:

```bash
# Server Configuration
SERVER_NAME=triple3-mcp
LOG_LEVEL=INFO

# File Paths
FILES_DIRECTORY=files
VECTOR_DB_PATH=data/vector_db
CACHE_DIRECTORY=data/cache

# Model Configuration
EMBEDDING_MODEL_NAME=sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
RERANKER_MODEL_NAME=cross-encoder/ms-marco-MiniLM-L-12-v2

# Search Configuration
DEFAULT_SEARCH_LIMIT=5
MAX_SEARCH_LIMIT=20
SIMILARITY_THRESHOLD=0.7

# Chunking Configuration
CHUNK_SIZE=512
CHUNK_OVERLAP=50
MIN_CHUNK_SIZE=100

# Cloud Run Configuration
PORT=8080
HOST=0.0.0.0
```

## Development

### Code Quality

The project uses several tools to maintain code quality:

- **Black**: Code formatting
- **isort**: Import sorting
- **flake8**: Linting
- **mypy**: Type checking
- **pre-commit**: Automated checks

Run quality checks:
```bash
# Format code
poetry run black src/ tests/

# Sort imports
poetry run isort src/ tests/

# Run linting
poetry run flake8 src/ tests/

# Type checking
poetry run mypy src/

# Run all pre-commit hooks
poetry run pre-commit run --all-files
```

### Testing

Run the test suite:
```bash
# Run all tests
poetry run pytest

# Run with coverage
poetry run pytest --cov=src/triple3_mcp --cov-report=html

# Run specific test file
poetry run pytest tests/test_server.py

# Run with verbose output
poetry run pytest -v
```

### Adding New Features

1. **Create a feature branch:**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Implement your changes** following the existing code patterns

3. **Add tests** for new functionality

4. **Update documentation** if needed

5. **Run quality checks:**
   ```bash
   poetry run pre-commit run --all-files
   poetry run pytest
   ```

6. **Commit using conventional commits:**
   ```bash
   git commit -m "feat: add new search feature"
   ```

## Deployment

### Google Cloud Run

1. **Build the Docker image:**
   ```bash
   docker build -f docker/Dockerfile -t triple3-mcp .
   ```

2. **Tag for Google Container Registry:**
   ```bash
   docker tag triple3-mcp gcr.io/your-project/triple3-mcp
   ```

3. **Push to registry:**
   ```bash
   docker push gcr.io/your-project/triple3-mcp
   ```

4. **Deploy to Cloud Run:**
   ```bash
   gcloud run deploy triple3-mcp \
     --image gcr.io/your-project/triple3-mcp \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated \
     --memory 2Gi \
     --cpu 2 \
     --timeout 300
   ```

### Environment Variables for Production

Set these environment variables in your Cloud Run service:

```bash
LOG_LEVEL=INFO
VECTOR_DB_PATH=/app/data/vector_db
CACHE_DIRECTORY=/app/data/cache
FILES_DIRECTORY=/app/files
```

## Technical Details

### Embedding Models

- **Primary**: `sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2`
  - Optimized for multilingual text including Chinese
  - 384-dimensional embeddings
  - Good balance of quality and performance

### Reranking

- **Model**: `cross-encoder/ms-marco-MiniLM-L-12-v2`
  - Cross-encoder architecture for better relevance scoring
  - Reranks top candidates from initial retrieval
  - Significantly improves result quality

### Text Processing

- **Chinese Segmentation**: Uses jieba for proper word boundaries
- **Semantic Chunking**: Respects sentence and paragraph boundaries
- **Overlap Strategy**: Maintains context between chunks

### Vector Database

- **ChromaDB**: Persistent vector storage with metadata
- **Cosine Similarity**: Default distance metric
- **Batch Processing**: Efficient bulk operations

## Troubleshooting

### Common Issues

1. **Model Download Failures**
   ```bash
   # Clear cache and retry
   rm -rf data/cache
   poetry run python -m triple3_mcp.server
   ```

2. **Memory Issues**
   ```bash
   # Reduce batch size in configuration
   export BATCH_SIZE=16
   ```

3. **PDF Processing Errors**
   ```bash
   # Check PDF file integrity
   python -c "import fitz; doc = fitz.open('files/your-file.pdf'); print(f'Pages: {len(doc)}')"
   ```

4. **ChromaDB Issues**
   ```bash
   # Reset vector database
   rm -rf data/vector_db
   poetry run python -m triple3_mcp.server
   ```

### Logging

The server provides detailed logging. Increase verbosity:

```bash
export LOG_LEVEL=DEBUG
poetry run python -m triple3_mcp.server
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests and documentation
5. Run quality checks
6. Submit a pull request

Please follow the [Conventional Commits](https://www.conventionalcommits.org/) specification for commit messages.

## License

This project is licensed under the MIT License. See the LICENSE file for details.

## Support

For questions or issues:

1. Check the [troubleshooting section](#troubleshooting)
2. Search existing [GitHub issues](https://github.com/ExcelsisCN/triple3-mcp/issues)
3. Create a new issue with detailed information

## Acknowledgments

- [Model Context Protocol](https://github.com/modelcontextprotocol) for the MCP specification
- [Sentence Transformers](https://www.sbert.net/) for embedding models
- [ChromaDB](https://www.trychroma.com/) for vector storage
- [PyMuPDF](https://pymupdf.readthedocs.io/) for PDF processing
